# 非接触式控制盘测试验证方案

## 🎯 **测试目标**

验证非接触式控制盘的所有功能是否符合设计要求，确保系统稳定可靠运行。

## 📋 **测试环境**

### 硬件环境
- STM32F407ZGT6开发板
- HC-SR04超声波模块
- 4个漫反射式光电开关
- 12V直流风扇 + H桥驱动
- LCD显示屏
- 万用表、示波器

### 软件环境
- Keil MDK-ARM 5.x
- STM32CubeMX
- 串口调试助手

## 🧪 **测试用例设计**

### 1. 超声波测距功能测试

**测试用例TC001: 测距范围验证**
```
测试步骤:
1. 将障碍物放置在5cm处，读取距离值
2. 逐步移动障碍物至30cm，每隔5cm记录一次
3. 验证测量值与实际值的误差

预期结果:
- 5-30cm范围内能正常测距
- 误差绝对值 ≤ 1cm
- 超出范围显示"Out of Range"
```

**测试用例TC002: 测距精度验证**
```
测试步骤:
1. 固定障碍物在10cm、15cm、20cm、25cm位置
2. 连续测量100次，记录数据
3. 计算平均值和标准差

预期结果:
- 平均误差 ≤ 1cm
- 标准差 ≤ 0.5cm
```

### 2. 光电开关功能测试

**测试用例TC003: 开关触发测试**
```
测试步骤:
1. 依次在S1-S4前挥手
2. 观察LCD显示和串口输出
3. 验证触发状态和时间记录

预期结果:
- 每个开关都能正确触发
- 状态变化及时响应
- 触发时间准确记录
```

**测试用例TC004: 开关抗干扰测试**
```
测试步骤:
1. 在强光环境下测试开关
2. 在多人环境下测试开关
3. 快速连续挥手测试

预期结果:
- 不受环境光干扰
- 不受其他人员干扰
- 能处理快速操作
```

### 3. 手势识别功能测试

**测试用例TC005: 基本手势识别**
```
测试步骤:
1. 按顺序执行所有8种手势
   - S1→S2, S2→S1, S3→S4, S4→S3
   - S4→S2, S3→S1, S3→S2, S4→S1
2. 记录识别结果和响应时间

预期结果:
- 所有手势都能正确识别
- 响应时间 < 100ms
- 无误识别和漏识别
```

**测试用例TC006: 手势时序测试**
```
测试步骤:
1. 测试不同速度的手势 (快速、正常、慢速)
2. 测试手势超时处理
3. 测试无效手势过滤

预期结果:
- 适应不同操作速度
- 超时后自动重置
- 过滤无效手势组合
```

### 4. 风扇控制功能测试

**测试用例TC007: 风扇启停控制**
```
测试步骤:
1. S1→S2启动正转，观察风扇状态
2. S2→S1停止正转，观察风扇状态
3. S3→S4启动反转，观察风扇状态
4. S4→S3停止反转，观察风扇状态

预期结果:
- 风扇能正确启动和停止
- 正反转方向正确
- 状态显示准确
```

**测试用例TC008: 风扇调速测试**
```
测试步骤:
1. 启动风扇正转
2. 多次执行S4→S2，观察电压变化
3. 多次执行S3→S1，观察电压变化
4. 用万用表测量实际电压

预期结果:
- 电压在3-10V范围内调节
- 每次调节步长约0.5V
- 显示电压与实际电压误差 ≤ 0.1V
```

### 5. 状态机功能测试

**测试用例TC009: 状态切换测试**
```
测试步骤:
1. 从IDLE状态切换到各个功能状态
2. 验证状态切换条件和逻辑
3. 测试状态超时处理

预期结果:
- 状态切换逻辑正确
- 超时后自动返回IDLE
- 状态显示实时更新
```

**测试用例TC010: 并发操作测试**
```
测试步骤:
1. 在风扇运行时执行其他手势
2. 在设定模式下执行无关手势
3. 测试异常情况处理

预期结果:
- 正确处理并发操作
- 优先级处理合理
- 异常情况能恢复
```

### 6. 高级功能测试

**测试用例TC011: 时间设定功能**
```
测试步骤:
1. 将手掌放在5-20cm范围内
2. 执行S3→S2手势
3. 验证时间设定和倒计时功能

预期结果:
- 距离-时间映射正确
- 倒计时显示准确
- 时间误差 ≤ 1s
```

**测试用例TC012: 电压设定功能**
```
测试步骤:
1. 将手掌放在5-20cm范围内
2. 执行S4→S1手势
3. 验证电压设定功能

预期结果:
- 距离-电压映射正确
- 电压设定准确
- 电压误差 ≤ 0.1V
```

## 📊 **测试数据记录表**

### 超声波测距精度测试记录
| 实际距离(cm) | 测量值1 | 测量值2 | 测量值3 | 平均值 | 误差 | 结果 |
|-------------|---------|---------|---------|--------|------|------|
| 5.0         |         |         |         |        |      |      |
| 10.0        |         |         |         |        |      |      |
| 15.0        |         |         |         |        |      |      |
| 20.0        |         |         |         |        |      |      |
| 25.0        |         |         |         |        |      |      |
| 30.0        |         |         |         |        |      |      |

### 手势识别测试记录
| 手势类型 | 测试次数 | 成功次数 | 成功率 | 平均响应时间 | 结果 |
|----------|----------|----------|--------|--------------|------|
| S1→S2    | 10       |          |        |              |      |
| S2→S1    | 10       |          |        |              |      |
| S3→S4    | 10       |          |        |              |      |
| S4→S3    | 10       |          |        |              |      |
| S4→S2    | 10       |          |        |              |      |
| S3→S1    | 10       |          |        |              |      |
| S3→S2    | 10       |          |        |              |      |
| S4→S1    | 10       |          |        |              |      |

### 风扇电压控制测试记录
| 设定电压(V) | 显示电压(V) | 实测电压(V) | 误差(V) | 结果 |
|-------------|-------------|-------------|---------|------|
| 3.0         |             |             |         |      |
| 4.0         |             |             |         |      |
| 5.0         |             |             |         |      |
| 6.0         |             |             |         |      |
| 7.0         |             |             |         |      |
| 8.0         |             |             |         |      |
| 9.0         |             |             |         |      |
| 10.0        |             |             |         |      |

## ✅ **验收标准**

### 基本要求验收
- [ ] 超声波测距范围5-30cm，误差≤1cm
- [ ] 风扇启停控制功能正常
- [ ] 风扇正反转控制功能正常  
- [ ] 风扇调速功能正常 (3-10V)
- [ ] 显示功能正常

### 发挥部分验收
- [ ] 时间设定功能正常，误差≤1s
- [ ] 电压设定功能正常，误差≤0.1V
- [ ] 组合操作功能正常
- [ ] 存储≥8个动作

### 性能要求验收
- [ ] 手势响应时间<100ms
- [ ] 系统稳定运行>1小时
- [ ] 抗干扰能力良好
- [ ] 界面显示清晰准确

## 🐛 **问题记录与解决**

### 问题记录表
| 问题ID | 问题描述 | 严重程度 | 发现时间 | 解决方案 | 解决时间 | 状态 |
|--------|----------|----------|----------|----------|----------|------|
| BUG001 |          | 高/中/低 |          |          |          | 开放/关闭 |
| BUG002 |          | 高/中/低 |          |          |          | 开放/关闭 |

## 📈 **测试报告模板**

```
测试报告
========

测试时间: YYYY-MM-DD
测试人员: XXX
测试版本: V1.0

测试概要:
- 测试用例总数: XX
- 通过用例数: XX  
- 失败用例数: XX
- 通过率: XX%

主要问题:
1. 问题描述及影响
2. 解决方案和状态

结论:
系统功能是否满足要求，是否可以发布。
```
