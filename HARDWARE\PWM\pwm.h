#ifndef __PWM_H
#define __PWM_H
#include "sys.h"

//////////////////////////////////////////////////////////////////////////////////	 
// STM32F407 PWM输出模块
// 使用TIM3_CH1输出PWM信号
// 引脚：PA6 (TIM3_CH1)
//////////////////////////////////////////////////////////////////////////////////

// PWM相关定义
#define PWM_TIM                TIM3
#define PWM_TIM_CLK_ENABLE()   __HAL_RCC_TIM3_CLK_ENABLE()
#define PWM_GPIO_PORT          GPIOA
#define PWM_GPIO_PIN           GPIO_PIN_6
#define PWM_GPIO_AF            GPIO_AF2_TIM3

// PWM参数
#define PWM_FREQUENCY          1000    // PWM频率 (Hz)
#define PWM_PERIOD             1000    // PWM周期计数值
#define PWM_MIN_DUTY           0       // 最小占空比
#define PWM_MAX_DUTY           1000    // 最大占空比

// 全局变量声明
extern TIM_HandleTypeDef htim_pwm;

// 函数声明
void PWM_Init(void);                           // PWM初始化
void PWM_SetDuty(uint16_t duty);              // 设置PWM占空比
void PWM_SetFrequency(uint32_t frequency);    // 设置PWM频率
void PWM_Start(void);                         // 启动PWM输出
void PWM_Stop(void);                          // 停止PWM输出
uint16_t PWM_GetDuty(void);                   // 获取当前占空比

#endif
