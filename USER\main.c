/************************************************
 STM32F407 非接触式控制盘主程序
 基于ALIENTEK 探索者STM32F407开发板

 【系统功能概述】
 本系统实现了基于手势识别的非接触式风扇控制，主要功能包括：
 1. 手势识别：通过4个光电开关检测挥手动作，识别8种手势组合
 2. 距离测量：超声波传感器测量5-30cm范围内的距离
 3. 风扇控制：PWM调速、正反转控制、启停控制
 4. 智能设定：基于距离的时间和电压设定功能
 5. 组合操作：可存储和执行复杂的操作序列

 【手势识别原理】
 - S1→S2: 依次触发光电开关S1和S2，实现风扇正转启动
 - S2→S1: 依次触发光电开关S2和S1，实现风扇正转停止
 - S3→S4: 依次触发光电开关S3和S4，实现风扇反转启动
 - S4→S3: 依次触发光电开关S4和S3，实现风扇反转停止
 - S4→S2: 运行时电压上升手势
 - S3→S1: 运行时电压下降手势
 - S3→S2: 距离5-20cm时的时间设定手势
 - S4→S1: 距离5-20cm时的电压设定手势

 【硬件连接】
 - 光电开关S1-S4: PC0-PC3 (漫反射式，低电平触发)
 - 超声波测距S5: PD3(TRIG), PD6(ECHO) (HC-SR04)
 - 风扇PWM: PA6 (TIM3_CH1, 1kHz频率)
 - 风扇方向: PA7, PG7 (H桥方向控制)
 - 风扇使能: PG8 (H桥使能控制)
 - LCD显示: 开发板自带TFTLCD

 技术支持：www.openedv.com
************************************************/

#include "sys.h"
#include "delay.h"
#include "usart.h"
#include "led.h"
#include "lcd.h"
#include "usmart.h"

// 硬件模块
#include "../HARDWARE/ULTRASONIC/ultrasonic.h"
#include "../HARDWARE/PHOTOSWITCH/photoswitch.h"
#include "../HARDWARE/FAN/fan.h"
#include "../HARDWARE/DAC/dac.h"

// 应用模块
#include "../APP/GESTURE/gesture.h"
#include "../APP/STATE_MACHINE/state_machine.h"

// 全局变量
float distance = 0;
GestureType_t current_gesture = GESTURE_NONE;
SystemState_t system_state = STATE_IDLE;
float dac_voltage = 0;  // DAC输出电压

// 显示相关变量
char display_str[50];
uint32_t last_display_update = 0;
#define DISPLAY_UPDATE_INTERVAL 100  // 100ms更新一次显示

// 函数声明
void System_Init(void);
void Display_Init(void);
void Display_Update(void);
void Control_Process(void);
void Control_ProcessTimeSetting(void);
void Control_ProcessVoltageSetting(void);
void Control_ProcessComboSetting(void);
void Control_ProcessComboRunning(void);

/**
 * @brief  主函数
 * @param  None
 * @retval None
 */
int main(void)
{
    // 系统初始化
    System_Init();
    
    // 显示初始化界面
    Display_Init();
    
    // 【主循环】- 系统核心控制逻辑
    // 采用轮询方式，以10ms为周期执行各个功能模块
    while(1)
    {
        // 【1. 传感器数据采集】
        // 读取超声波距离传感器数据，用于距离相关的功能
        distance = Ultrasonic_GetDistance();

        // 【1.1 DAC输出控制】
        // 根据测量距离控制DAC输出电压
        if(Ultrasonic_IsInRange(distance))
        {
            // 距离范围5-20cm映射到DAC电压3.3 - 0V（对应的点击电压为10.5 - 3.0v）
            // 距离越近，电压越高
            dac_voltage = 3.3f - ((distance - 5.0f) / 15.0f) * 3.3f;
            if(dac_voltage > 3.3f) dac_voltage = 3.3f;
            if(dac_voltage < 0) dac_voltage = 0;
            DAC_SetVoltage(dac_voltage);
        }
        else
        {
            // 超出范围时DAC输出0V
            dac_voltage = 0;
            DAC_SetVoltage(0);
        }

        // 【2. 输入设备扫描】
        // 扫描4个光电开关的状态变化，为手势识别提供原始数据
        PhotoSwitch_Scan();

        // 【3. 手势识别处理】
        // 基于光电开关状态变化，识别用户的挥手手势
        // 返回识别到的手势类型，如S1→S2、S3→S4等
        current_gesture = Gesture_Recognize();

        // 【4. 状态机逻辑处理】
        // 根据当前状态和识别到的手势，执行相应的控制逻辑
        // 包括状态转换、风扇控制、参数设定等
        StateMachine_Process(current_gesture);

        // 【5. 控制逻辑处理】
        // 根据当前系统状态，执行特定的控制任务
        Control_Process();

        // 【6. 显示界面更新】
        // 每100ms更新一次LCD显示，避免频繁刷新影响性能
        if(HAL_GetTick() - last_display_update > DISPLAY_UPDATE_INTERVAL)
        {
            Display_Update();
            last_display_update = HAL_GetTick();
        }

        // 【7. 系统维护任务】
        // 状态机超时检查和自动状态管理
        StateMachine_Update();

        // 风扇运行状态更新和保护逻辑
        Fan_Update();

        // 【8. 状态指示】
        // LED指示灯显示系统运行状态
        if(Fan_IsRunning())
        {
            LED0 = !LED0;  // 风扇运行时LED闪烁，提供视觉反馈
        }
        else
        {
            LED0 = 1;      // 风扇停止时LED熄灭
        }

        // 【9. 系统节拍控制】
        // 10ms延时，控制主循环执行频率，平衡响应性和系统负载
        delay_ms(10);
    }
}

/**
 * @brief  系统初始化
 * @param  None
 * @retval None
 */
void System_Init(void)
{
    HAL_Init();                     // 初始化HAL库
    Stm32_Clock_Init(336,8,2,7);    // 设置时钟,168Mhz
    delay_init(168);                // 延时初始化
    uart_init(115200);              // 串口初始化
    LED_Init();                     // LED初始化
    LCD_Init();                     // LCD初始化
    usmart_dev.init(84);            // 初始化USMART
    
    // 硬件模块初始化
    Ultrasonic_Init();              // 超声波初始化
    PhotoSwitch_Init();             // 光电开关初始化
    Fan_Init();                     // 风扇控制初始化
    DAC_Init();                     // DAC初始化

    // 应用模块初始化
    Gesture_Init();                 // 手势识别初始化
    StateMachine_Init();            // 状态机初始化
	
	
	LCD_Clear(WHITE);               // 清屏为白色
	POINT_COLOR = GREEN;
	LCD_ShowString(30,360,300,16,24,"Everything is ok！");
}

/**
 * @brief  显示初始化
 * @param  None
 * @retval None
 */
void Display_Init(void)
{
    // 显示标题
    POINT_COLOR = BLUE;             // 画笔颜色：蓝色
    LCD_ShowString(30,10,300,24,24,"STM32F407 Control Panel");
    LCD_ShowString(30,40,300,16,16,"Non-Contact Fan Control");

    // 显示状态标签
    POINT_COLOR = RED;
    LCD_ShowString(30,70,300,16,16,"Distance (Exact):");
    LCD_ShowString(30,90,300,16,16,"Distance (Rounded):");
    LCD_ShowString(30,120,300,16,16,"System State:");
    LCD_ShowString(30,150,300,16,16,"Fan Status:");
    LCD_ShowString(30,180,300,16,16,"Fan Voltage:");
    LCD_ShowString(30,210,300,16,16,"Current Gesture:");

    // 光电门状态标签
    POINT_COLOR = DARKBLUE;
    LCD_ShowString(30,270,300,16,16,"PhotoSwitches: S1    S2    S3    S4");
    LCD_ShowString(30,300,300,16,16,"Command Status:");
    LCD_ShowString(30,330,300,16,16,"DAC Output:");
}

/**
 * @brief  显示更新
 * @param  None
 * @retval None
 */
void Display_Update(void)
{
	GPIO_PinState s1_state;
	GPIO_PinState s2_state;
	GPIO_PinState s3_state;
	GPIO_PinState s4_state;
	
    // 显示精确距离值
    if(Ultrasonic_IsInRange(distance))
    {
        sprintf(display_str, "%.2f cm  ", distance);
        POINT_COLOR = BLACK;
    }
    else
    {
        sprintf(display_str, "Out of Range");
        POINT_COLOR = RED;
    }
    LCD_ShowString(180,70,120,16,16,(u8*)display_str);

    // 显示四舍五入后的距离值
    if(Ultrasonic_IsInRange(distance))
    {
        int rounded_distance = (int)(distance + 0.5f);  // 四舍五入
        sprintf(display_str, "%d cm  ", rounded_distance);
        POINT_COLOR = BLUE;
    }
    else
    {
        sprintf(display_str, "---");
        POINT_COLOR = RED;
    }
    LCD_ShowString(180,90,120,16,16,(u8*)display_str);
    
    // 显示系统状态
    system_state = StateMachine_GetState();
    sprintf(display_str, "%s    ", StateMachine_GetStateName(system_state));
    POINT_COLOR = MAGENTA;
    LCD_ShowString(150,120,150,16,16,(u8*)display_str);

    // 显示风扇状态
    if(Fan_IsRunning())
    {
        FanDirection_t direction = Fan_GetDirection();
        sprintf(display_str, "%s Running",
                direction == FAN_FORWARD ? "Forward" : "Reverse");
        POINT_COLOR = GREEN;
    }
    else
    {
        sprintf(display_str, "Stopped    ");
        POINT_COLOR = RED;
    }
    LCD_ShowString(150,150,150,16,16,(u8*)display_str);

    // 显示风扇电压
    sprintf(display_str, "%.1fV    ", Fan_GetVoltage() / 1000.0f);
    POINT_COLOR = BROWN;
    LCD_ShowString(150,180,100,16,16,(u8*)display_str);

    // 显示手势
    if(current_gesture != GESTURE_NONE)
    {
        sprintf(display_str, "%s    ", Gesture_GetName(current_gesture));
        POINT_COLOR = MAGENTA;
        LCD_ShowString(180,210,120,16,16,(u8*)display_str);
    }
    else
    {
        sprintf(display_str, "None    ");
        POINT_COLOR = GRAY;
        LCD_ShowString(180,210,120,16,16,(u8*)display_str);
    }

    // 显示4个光电门的触发状态
    // S1状态 (PC0)
    s1_state = HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_0);
    if(s1_state == GPIO_PIN_RESET)  // 低电平表示触发
    {
        POINT_COLOR = GREEN;
        LCD_ShowString(170,270,30,16,16,"ON ");
    }
    else
    {
        POINT_COLOR = RED;
        LCD_ShowString(170,270,30,16,16,"OFF");
    }

    // S2状态 (PC1)
    s2_state = HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_1);
    if(s2_state == GPIO_PIN_RESET)  // 低电平表示触发
    {
        POINT_COLOR = GREEN;
        LCD_ShowString(210,270,30,16,16,"ON ");
    }
    else
    {
        POINT_COLOR = RED;
        LCD_ShowString(210,270,30,16,16,"OFF");
    }

    // S3状态 (PC2)
    s3_state = HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_2);
    if(s3_state == GPIO_PIN_RESET)  // 低电平表示触发
    {
        POINT_COLOR = GREEN;
        LCD_ShowString(250,270,30,16,16,"ON ");
    }
    else
    {
        POINT_COLOR = RED;
        LCD_ShowString(250,270,30,16,16,"OFF");
    }

    // S4状态 (PC3)
    s4_state = HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_3);
    if(s4_state == GPIO_PIN_RESET)  // 低电平表示触发
    {
        POINT_COLOR = GREEN;
        LCD_ShowString(290,270,30,16,16,"ON ");
    }
    else
    {
        POINT_COLOR = RED;
        LCD_ShowString(290,270,30,16,16,"OFF");
    }

    // 显示当前命令状态
    if(current_gesture != GESTURE_NONE)
    {
        // 根据手势显示对应的命令
        const char* command_str = "";
        switch(current_gesture)
        {
            case GESTURE_S1_TO_S2:
                command_str = "CMD: Fan Forward Start";
                break;
            case GESTURE_S2_TO_S1:
                command_str = "CMD: Fan Forward Stop";
                break;
            case GESTURE_S3_TO_S4:
                command_str = "CMD: Fan Reverse Start";
                break;
            case GESTURE_S4_TO_S3:
                command_str = "CMD: Fan Reverse Stop";
                break;
            case GESTURE_S4_TO_S2:
                command_str = "CMD: Voltage Up";
                break;
            case GESTURE_S3_TO_S1:
                command_str = "CMD: Voltage Down";
                break;
            case GESTURE_S3_TO_S2:
                command_str = "CMD: Time Setting";
                break;
            case GESTURE_S4_TO_S1:
                command_str = "CMD: Voltage Setting";
                break;
            default:
                command_str = "CMD: Unknown";
                break;
        }
        sprintf(display_str, "%s    ", command_str);
        POINT_COLOR = BLUE;
    }
    else
    {
        sprintf(display_str, "CMD: Waiting for gesture...    ");
        POINT_COLOR = GRAY;
    }
    LCD_ShowString(150,300,300,16,16,(u8*)display_str);

    // 显示DAC输出电压
    sprintf(display_str, "%.2fV (Distance: %.1fcm)    ", dac_voltage, distance);
    POINT_COLOR = BRRED;
    LCD_ShowString(150,330,300,16,16,(u8*)display_str);
}

/**
 * @brief  控制处理
 * @param  None
 * @retval None
 */
void Control_Process(void)
{
    // 根据当前状态执行相应的控制逻辑
    switch(system_state)
    {
        case STATE_IDLE:
            // 待机状态，无特殊处理
            break;
            
        case STATE_FAN_CONTROL:
            // 风扇控制状态，已在状态机中处理
            break;
            
        case STATE_TIME_SETTING:
            // 时间设定状态处理
            Control_ProcessTimeSetting();
            break;
            
        case STATE_VOLTAGE_SETTING:
            // 电压设定状态处理
            Control_ProcessVoltageSetting();
            break;
            
        case STATE_COMBO_SETTING:
            // 组合操作设定状态处理
            Control_ProcessComboSetting();
            break;
            
        case STATE_COMBO_RUNNING:
            // 组合操作运行状态处理
            Control_ProcessComboRunning();
            break;
            
        default:
            break;
    }
}

// USMART调试函数
void led_set(u8 sta)
{
    LED0 = sta;
}

void test_fun(void)
{
    printf("System Status:\r\n");
    printf("Distance: %.1f cm\r\n", distance);
    printf("State: %s\r\n", StateMachine_GetStateName(system_state));
    printf("Fan: %s\r\n", Fan_IsRunning() ? "Running" : "Stopped");
    printf("Voltage: %.1fV\r\n", Fan_GetVoltage() / 1000.0f);
}

/**
 * @brief  时间设定状态处理
 * @param  None
 * @retval None
 */
void Control_ProcessTimeSetting(void)
{
    // 时间设定状态的控制逻辑
    // 在状态机中已处理主要逻辑，这里可以添加额外的显示或处理
}

/**
 * @brief  电压设定状态处理
 * @param  None
 * @retval None
 */
void Control_ProcessVoltageSetting(void)
{
    // 电压设定状态的控制逻辑
    // 在状态机中已处理主要逻辑，这里可以添加额外的显示或处理
}

/**
 * @brief  组合操作设定状态处理
 * @param  None
 * @retval None
 */
void Control_ProcessComboSetting(void)
{
    // 组合操作设定状态的控制逻辑
    // TODO: 实现组合操作的用户界面和存储逻辑
}

/**
 * @brief  组合操作运行状态处理
 * @param  None
 * @retval None
 */
void Control_ProcessComboRunning(void)
{
    // 组合操作运行状态的控制逻辑
    // TODO: 实现组合操作的执行逻辑
}

