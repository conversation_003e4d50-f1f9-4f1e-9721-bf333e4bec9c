Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    main.o(i.Display_Init) refers to lcd.o(i.LCD_ShowString) for LCD_ShowString
    main.o(i.Display_Init) refers to lcd.o(.data) for POINT_COLOR
    main.o(i.Display_Update) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.Display_Update) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    main.o(i.Display_Update) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    main.o(i.Display_Update) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    main.o(i.Display_Update) refers to _printf_dec.o(.text) for _printf_int_dec
    main.o(i.Display_Update) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    main.o(i.Display_Update) refers to _printf_str.o(.text) for _printf_str
    main.o(i.Display_Update) refers to ultrasonic.o(i.Ultrasonic_IsInRange) for Ultrasonic_IsInRange
    main.o(i.Display_Update) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    main.o(i.Display_Update) refers to noretval__2sprintf.o(.text) for __2sprintf
    main.o(i.Display_Update) refers to lcd.o(i.LCD_ShowString) for LCD_ShowString
    main.o(i.Display_Update) refers to state_machine.o(i.StateMachine_GetState) for StateMachine_GetState
    main.o(i.Display_Update) refers to state_machine.o(i.StateMachine_GetStateName) for StateMachine_GetStateName
    main.o(i.Display_Update) refers to fan.o(i.Fan_IsRunning) for Fan_IsRunning
    main.o(i.Display_Update) refers to fan.o(i.Fan_GetDirection) for Fan_GetDirection
    main.o(i.Display_Update) refers to fan.o(i.Fan_GetVoltage) for Fan_GetVoltage
    main.o(i.Display_Update) refers to gesture.o(i.Gesture_GetName) for Gesture_GetName
    main.o(i.Display_Update) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    main.o(i.Display_Update) refers to main.o(.data) for .data
    main.o(i.Display_Update) refers to lcd.o(.data) for POINT_COLOR
    main.o(i.Display_Update) refers to main.o(.bss) for .bss
    main.o(i.System_Init) refers to stm32f4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.System_Init) refers to sys.o(i.Stm32_Clock_Init) for Stm32_Clock_Init
    main.o(i.System_Init) refers to delay.o(i.delay_init) for delay_init
    main.o(i.System_Init) refers to usart.o(i.uart_init) for uart_init
    main.o(i.System_Init) refers to led.o(i.LED_Init) for LED_Init
    main.o(i.System_Init) refers to lcd.o(i.LCD_Init) for LCD_Init
    main.o(i.System_Init) refers to ultrasonic.o(i.Ultrasonic_Init) for Ultrasonic_Init
    main.o(i.System_Init) refers to photoswitch.o(i.PhotoSwitch_Init) for PhotoSwitch_Init
    main.o(i.System_Init) refers to fan.o(i.Fan_Init) for Fan_Init
    main.o(i.System_Init) refers to dac.o(i.DAC_Init) for DAC_Init
    main.o(i.System_Init) refers to gesture.o(i.Gesture_Init) for Gesture_Init
    main.o(i.System_Init) refers to state_machine.o(i.StateMachine_Init) for StateMachine_Init
    main.o(i.System_Init) refers to lcd.o(i.LCD_Clear) for LCD_Clear
    main.o(i.System_Init) refers to lcd.o(i.LCD_ShowString) for LCD_ShowString
    main.o(i.System_Init) refers to usmart_config.o(.data) for usmart_dev
    main.o(i.System_Init) refers to lcd.o(.data) for POINT_COLOR
    main.o(i.main) refers to main.o(i.System_Init) for System_Init
    main.o(i.main) refers to main.o(i.Display_Init) for Display_Init
    main.o(i.main) refers to ultrasonic.o(i.Ultrasonic_GetDistance) for Ultrasonic_GetDistance
    main.o(i.main) refers to ultrasonic.o(i.Ultrasonic_IsInRange) for Ultrasonic_IsInRange
    main.o(i.main) refers to dac.o(i.DAC_SetVoltage) for DAC_SetVoltage
    main.o(i.main) refers to photoswitch.o(i.PhotoSwitch_Scan) for PhotoSwitch_Scan
    main.o(i.main) refers to gesture.o(i.Gesture_Recognize) for Gesture_Recognize
    main.o(i.main) refers to state_machine.o(i.StateMachine_Process) for StateMachine_Process
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    main.o(i.main) refers to main.o(i.Display_Update) for Display_Update
    main.o(i.main) refers to state_machine.o(i.StateMachine_Update) for StateMachine_Update
    main.o(i.main) refers to fan.o(i.Fan_Update) for Fan_Update
    main.o(i.main) refers to fan.o(i.Fan_IsRunning) for Fan_IsRunning
    main.o(i.main) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.main) refers to main.o(.data) for .data
    main.o(i.test_fun) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.test_fun) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    main.o(i.test_fun) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    main.o(i.test_fun) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    main.o(i.test_fun) refers to _printf_str.o(.text) for _printf_str
    main.o(i.test_fun) refers to noretval__2printf.o(.text) for __2printf
    main.o(i.test_fun) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    main.o(i.test_fun) refers to state_machine.o(i.StateMachine_GetStateName) for StateMachine_GetStateName
    main.o(i.test_fun) refers to fan.o(i.Fan_IsRunning) for Fan_IsRunning
    main.o(i.test_fun) refers to fan.o(i.Fan_GetVoltage) for Fan_GetVoltage
    main.o(i.test_fun) refers to main.o(.data) for .data
    stm32f4xx_it.o(i.SysTick_Handler) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for .data
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.constdata) for .constdata
    startup_stm32f407xx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(RESET) refers to usmart.o(i.TIM4_IRQHandler) for TIM4_IRQHandler
    startup_stm32f407xx.o(RESET) refers to usart.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f407xx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f407xx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(HEAP) for Heap_Mem
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(STACK) for Stack_Mem
    stm32f4xx_hal.o(i.HAL_DeInit) refers to stm32f4xx_hal_msp.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(i.HAL_GetTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_IncTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(i.HAL_InitTick) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(i.NVIC_SetPriority) for NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(i.NVIC_SetPriority) for NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) refers to stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Transmit_IT) for UART_Transmit_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to usart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to usart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_usart.o(i.HAL_USART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_usart.o(i.HAL_USART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_usart.o(i.HAL_USART_Abort_IT) refers to stm32f4xx_hal_usart.o(i.HAL_USART_AbortCpltCallback) for HAL_USART_AbortCpltCallback
    stm32f4xx_hal_usart.o(i.HAL_USART_Abort_IT) refers to stm32f4xx_hal_usart.o(i.USART_DMATxAbortCallback) for USART_DMATxAbortCallback
    stm32f4xx_hal_usart.o(i.HAL_USART_Abort_IT) refers to stm32f4xx_hal_usart.o(i.USART_DMARxAbortCallback) for USART_DMARxAbortCallback
    stm32f4xx_hal_usart.o(i.HAL_USART_DMAStop) refers to stm32f4xx_hal_usart.o(i.USART_EndTxTransfer) for USART_EndTxTransfer
    stm32f4xx_hal_usart.o(i.HAL_USART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_usart.o(i.HAL_USART_DMAStop) refers to stm32f4xx_hal_usart.o(i.USART_EndRxTransfer) for USART_EndRxTransfer
    stm32f4xx_hal_usart.o(i.HAL_USART_DeInit) refers to stm32f4xx_hal_usart.o(i.HAL_USART_MspDeInit) for HAL_USART_MspDeInit
    stm32f4xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32f4xx_hal_usart.o(i.USART_TransmitReceive_IT) for USART_TransmitReceive_IT
    stm32f4xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32f4xx_hal_usart.o(i.USART_EndRxTransfer) for USART_EndRxTransfer
    stm32f4xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32f4xx_hal_usart.o(i.USART_Receive_IT) for USART_Receive_IT
    stm32f4xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32f4xx_hal_usart.o(i.HAL_USART_ErrorCallback) for HAL_USART_ErrorCallback
    stm32f4xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32f4xx_hal_usart.o(i.USART_Transmit_IT) for USART_Transmit_IT
    stm32f4xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32f4xx_hal_usart.o(i.HAL_USART_TxCpltCallback) for HAL_USART_TxCpltCallback
    stm32f4xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32f4xx_hal_usart.o(i.USART_DMAAbortOnError) for USART_DMAAbortOnError
    stm32f4xx_hal_usart.o(i.HAL_USART_Init) refers to stm32f4xx_hal_usart.o(i.HAL_USART_MspInit) for HAL_USART_MspInit
    stm32f4xx_hal_usart.o(i.HAL_USART_Init) refers to stm32f4xx_hal_usart.o(i.USART_SetConfig) for USART_SetConfig
    stm32f4xx_hal_usart.o(i.HAL_USART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_usart.o(i.HAL_USART_Receive) refers to stm32f4xx_hal_usart.o(i.USART_WaitOnFlagUntilTimeout) for USART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_usart.o(i.HAL_USART_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_usart.o(i.HAL_USART_Receive_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMAReceiveCplt) for USART_DMAReceiveCplt
    stm32f4xx_hal_usart.o(i.HAL_USART_Receive_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMARxHalfCplt) for USART_DMARxHalfCplt
    stm32f4xx_hal_usart.o(i.HAL_USART_Receive_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMAError) for USART_DMAError
    stm32f4xx_hal_usart.o(i.HAL_USART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_usart.o(i.HAL_USART_Transmit) refers to stm32f4xx_hal_usart.o(i.USART_WaitOnFlagUntilTimeout) for USART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive) refers to stm32f4xx_hal_usart.o(i.USART_WaitOnFlagUntilTimeout) for USART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMAReceiveCplt) for USART_DMAReceiveCplt
    stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMARxHalfCplt) for USART_DMARxHalfCplt
    stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMATransmitCplt) for USART_DMATransmitCplt
    stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMATxHalfCplt) for USART_DMATxHalfCplt
    stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMAError) for USART_DMAError
    stm32f4xx_hal_usart.o(i.HAL_USART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_usart.o(i.HAL_USART_Transmit_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMATransmitCplt) for USART_DMATransmitCplt
    stm32f4xx_hal_usart.o(i.HAL_USART_Transmit_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMATxHalfCplt) for USART_DMATxHalfCplt
    stm32f4xx_hal_usart.o(i.HAL_USART_Transmit_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMAError) for USART_DMAError
    stm32f4xx_hal_usart.o(i.USART_DMAAbortOnError) refers to stm32f4xx_hal_usart.o(i.HAL_USART_ErrorCallback) for HAL_USART_ErrorCallback
    stm32f4xx_hal_usart.o(i.USART_DMAError) refers to stm32f4xx_hal_usart.o(i.USART_EndTxTransfer) for USART_EndTxTransfer
    stm32f4xx_hal_usart.o(i.USART_DMAError) refers to stm32f4xx_hal_usart.o(i.USART_EndRxTransfer) for USART_EndRxTransfer
    stm32f4xx_hal_usart.o(i.USART_DMAError) refers to stm32f4xx_hal_usart.o(i.HAL_USART_ErrorCallback) for HAL_USART_ErrorCallback
    stm32f4xx_hal_usart.o(i.USART_DMAReceiveCplt) refers to stm32f4xx_hal_usart.o(i.HAL_USART_TxRxCpltCallback) for HAL_USART_TxRxCpltCallback
    stm32f4xx_hal_usart.o(i.USART_DMAReceiveCplt) refers to stm32f4xx_hal_usart.o(i.HAL_USART_RxCpltCallback) for HAL_USART_RxCpltCallback
    stm32f4xx_hal_usart.o(i.USART_DMARxAbortCallback) refers to stm32f4xx_hal_usart.o(i.HAL_USART_AbortCpltCallback) for HAL_USART_AbortCpltCallback
    stm32f4xx_hal_usart.o(i.USART_DMARxHalfCplt) refers to stm32f4xx_hal_usart.o(i.HAL_USART_RxHalfCpltCallback) for HAL_USART_RxHalfCpltCallback
    stm32f4xx_hal_usart.o(i.USART_DMATransmitCplt) refers to stm32f4xx_hal_usart.o(i.HAL_USART_TxCpltCallback) for HAL_USART_TxCpltCallback
    stm32f4xx_hal_usart.o(i.USART_DMATxAbortCallback) refers to stm32f4xx_hal_usart.o(i.HAL_USART_AbortCpltCallback) for HAL_USART_AbortCpltCallback
    stm32f4xx_hal_usart.o(i.USART_DMATxHalfCplt) refers to stm32f4xx_hal_usart.o(i.HAL_USART_TxHalfCpltCallback) for HAL_USART_TxHalfCpltCallback
    stm32f4xx_hal_usart.o(i.USART_Receive_IT) refers to stm32f4xx_hal_usart.o(i.HAL_USART_RxCpltCallback) for HAL_USART_RxCpltCallback
    stm32f4xx_hal_usart.o(i.USART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_usart.o(i.USART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_usart.o(i.USART_TransmitReceive_IT) refers to stm32f4xx_hal_usart.o(i.HAL_USART_TxRxCpltCallback) for HAL_USART_TxRxCpltCallback
    stm32f4xx_hal_usart.o(i.USART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32f4xx_hal_dma.o(.constdata) for .constdata
    stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutationCallback) for HAL_TIMEx_CommutationCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchronization) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchronization_IT) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAError) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutationEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutationEvent_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutationCallback) for HAL_TIMEx_CommutationCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f4xx_hal_spi.o(i.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f4xx_hal_spi.o(i.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.SPI_DMATxAbortCallback) for SPI_DMATxAbortCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback) for SPI_DMARxAbortCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_spi.o(i.HAL_SPI_DeInit) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_MspDeInit) for HAL_SPI_MspDeInit
    stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f4xx_hal_spi.o(i.SPI_DMAAbortOnError) for SPI_DMAAbortOnError
    stm32f4xx_hal_spi.o(i.HAL_SPI_Init) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_MspInit) for HAL_SPI_MspInit
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) for HAL_SPI_TransmitReceive_DMA
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) for HAL_SPI_TransmitReceive_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_RxISR_16BIT) for SPI_RxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_RxISR_8BIT) for SPI_RxISR_8BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32f4xx_hal_spi.o(i.SPI_CheckFlag_BSY) for SPI_CheckFlag_BSY
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32f4xx_hal_spi.o(i.SPI_CheckFlag_BSY) for SPI_CheckFlag_BSY
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) for SPI_DMAHalfTransmitReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) for SPI_DMATransmitReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) for SPI_2linesRxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) for SPI_2linesTxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) for SPI_2linesRxISR_8BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) for SPI_2linesTxISR_8BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) for SPI_DMAHalfTransmitCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) for SPI_DMATransmitCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32f4xx_hal_spi.o(i.SPI_TxISR_16BIT) for SPI_TxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32f4xx_hal_spi.o(i.SPI_TxISR_8BIT) for SPI_TxISR_8BIT
    stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BITCRC) for SPI_2linesRxISR_16BITCRC
    stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BITCRC) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BITCRC) for SPI_2linesRxISR_8BITCRC
    stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BITCRC) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_AbortRx_ISR) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_CheckFlag_BSY) refers to stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.SPI_CheckFlag_BSY) for SPI_CheckFlag_BSY
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f4xx_hal_spi.o(i.SPI_CheckFlag_BSY) for SPI_CheckFlag_BSY
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_DMAAbortOnError) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAError) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback) for HAL_SPI_RxHalfCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback) for HAL_SPI_TxHalfCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback) for HAL_SPI_TxRxHalfCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(i.SPI_CheckFlag_BSY) for SPI_CheckFlag_BSY
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.SPI_CheckFlag_BSY) for SPI_CheckFlag_BSY
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_RxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f4xx_hal_spi.o(i.SPI_RxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_RxISR_16BITCRC) for SPI_RxISR_16BITCRC
    stm32f4xx_hal_spi.o(i.SPI_RxISR_16BITCRC) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f4xx_hal_spi.o(i.SPI_RxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f4xx_hal_spi.o(i.SPI_RxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_RxISR_8BITCRC) for SPI_RxISR_8BITCRC
    stm32f4xx_hal_spi.o(i.SPI_RxISR_8BITCRC) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f4xx_hal_spi.o(i.SPI_TxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_TxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_sram.o(i.HAL_SRAM_DeInit) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_MspDeInit) for HAL_SRAM_MspDeInit
    stm32f4xx_hal_sram.o(i.HAL_SRAM_DeInit) refers to stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_DeInit) for FSMC_NORSRAM_DeInit
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to lcd.o(i.HAL_SRAM_MspInit) for HAL_SRAM_MspInit
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Init) for FSMC_NORSRAM_Init
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Timing_Init) for FSMC_NORSRAM_Timing_Init
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Extended_Timing_Init) for FSMC_NORSRAM_Extended_Timing_Init
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferCpltCallback) for HAL_SRAM_DMA_XferCpltCallback
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferErrorCallback) for HAL_SRAM_DMA_XferErrorCallback
    stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Disable) refers to stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_WriteOperation_Disable) for FSMC_NORSRAM_WriteOperation_Disable
    stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Enable) refers to stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_WriteOperation_Enable) for FSMC_NORSRAM_WriteOperation_Enable
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferCpltCallback) for HAL_SRAM_DMA_XferCpltCallback
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferErrorCallback) for HAL_SRAM_DMA_XferErrorCallback
    stm32f4xx_ll_fsmc.o(i.FSMC_NAND_GetECC) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dac.o(i.DAC_DMAConvCpltCh1) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_ConvCpltCallbackCh1) for HAL_DAC_ConvCpltCallbackCh1
    stm32f4xx_hal_dac.o(i.DAC_DMAErrorCh1) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_ErrorCallbackCh1) for HAL_DAC_ErrorCallbackCh1
    stm32f4xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_ConvHalfCpltCallbackCh1) for HAL_DAC_ConvHalfCpltCallbackCh1
    stm32f4xx_hal_dac.o(i.HAL_DAC_DeInit) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_MspDeInit) for HAL_DAC_MspDeInit
    stm32f4xx_hal_dac.o(i.HAL_DAC_IRQHandler) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_DMAUnderrunCallbackCh1) for HAL_DAC_DMAUnderrunCallbackCh1
    stm32f4xx_hal_dac.o(i.HAL_DAC_IRQHandler) refers to stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_DMAUnderrunCallbackCh2) for HAL_DACEx_DMAUnderrunCallbackCh2
    stm32f4xx_hal_dac.o(i.HAL_DAC_Init) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_MspInit) for HAL_DAC_MspInit
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2) for DAC_DMAConvCpltCh2
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2) for DAC_DMAHalfConvCpltCh2
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac_ex.o(i.DAC_DMAErrorCh2) for DAC_DMAErrorCh2
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac.o(i.DAC_DMAConvCpltCh1) for DAC_DMAConvCpltCh1
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1) for DAC_DMAHalfConvCpltCh1
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac.o(i.DAC_DMAErrorCh1) for DAC_DMAErrorCh1
    stm32f4xx_hal_dac.o(i.HAL_DAC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2) refers to stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ConvCpltCallbackCh2) for HAL_DACEx_ConvCpltCallbackCh2
    stm32f4xx_hal_dac_ex.o(i.DAC_DMAErrorCh2) refers to stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ErrorCallbackCh2) for HAL_DACEx_ErrorCallbackCh2
    stm32f4xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2) refers to stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ConvHalfCpltCallbackCh2) for HAL_DACEx_ConvHalfCpltCallbackCh2
    delay.o(i.delay_init) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig) for HAL_SYSTICK_CLKSourceConfig
    delay.o(i.delay_init) refers to delay.o(.data) for .data
    delay.o(i.delay_ms) refers to delay.o(i.delay_us) for delay_us
    delay.o(i.delay_us) refers to delay.o(.data) for .data
    sys.o(i.Stm32_Clock_Init) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    sys.o(i.Stm32_Clock_Init) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    sys.o(i.Stm32_Clock_Init) refers to stm32f4xx_hal.o(i.HAL_GetREVID) for HAL_GetREVID
    usart.o(.rev16_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.revsh_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.rrx_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.HAL_UART_MspInit) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_RxCpltCallback) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.HAL_UART_RxCpltCallback) refers to usart.o(.data) for .data
    usart.o(i.HAL_UART_RxCpltCallback) refers to usart.o(.bss) for .bss
    usart.o(i.USART1_IRQHandler) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_GetState) for HAL_UART_GetState
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    usart.o(i.USART1_IRQHandler) refers to usart.o(.bss) for .bss
    usart.o(i.USART1_IRQHandler) refers to usart.o(.data) for .data
    usart.o(i._sys_exit) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.fputc) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart_init) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart_init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.uart_init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    usart.o(i.uart_init) refers to usart.o(.bss) for .bss
    usart.o(i.uart_init) refers to usart.o(.data) for .data
    usart.o(.bss) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    led.o(i.LED_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    led.o(i.LED_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    lcd.o(i.HAL_SRAM_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    lcd.o(i.LCD_Clear) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_Clear) refers to lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lcd.o(i.LCD_Clear) refers to lcd.o(.bss) for .bss
    lcd.o(i.LCD_Color_Fill) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_Color_Fill) refers to lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lcd.o(i.LCD_DisplayOff) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_DisplayOff) refers to lcd.o(.bss) for .bss
    lcd.o(i.LCD_DisplayOn) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_DisplayOn) refers to lcd.o(.bss) for .bss
    lcd.o(i.LCD_Display_Dir) refers to lcd.o(i.LCD_Scan_Dir) for LCD_Scan_Dir
    lcd.o(i.LCD_Display_Dir) refers to lcd.o(.bss) for .bss
    lcd.o(i.LCD_DrawLine) refers to lcd.o(i.LCD_DrawPoint) for LCD_DrawPoint
    lcd.o(i.LCD_DrawPoint) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_DrawPoint) refers to lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lcd.o(i.LCD_DrawPoint) refers to lcd.o(.data) for .data
    lcd.o(i.LCD_DrawRectangle) refers to lcd.o(i.LCD_DrawLine) for LCD_DrawLine
    lcd.o(i.LCD_Draw_Circle) refers to lcd.o(i.LCD_DrawPoint) for LCD_DrawPoint
    lcd.o(i.LCD_Fast_DrawPoint) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_Fast_DrawPoint) refers to lcd.o(i.LCD_WR_DATA) for LCD_WR_DATA
    lcd.o(i.LCD_Fast_DrawPoint) refers to lcd.o(.bss) for .bss
    lcd.o(i.LCD_Fill) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_Fill) refers to lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lcd.o(i.LCD_Init) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    lcd.o(i.LCD_Init) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    lcd.o(i.LCD_Init) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    lcd.o(i.LCD_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    lcd.o(i.LCD_Init) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) for HAL_SRAM_Init
    lcd.o(i.LCD_Init) refers to delay.o(i.delay_ms) for delay_ms
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_RD_DATA) for LCD_RD_DATA
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.LCD_Init) refers to noretval__2printf.o(.text) for __2printf
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_WR_DATA) for LCD_WR_DATA
    lcd.o(i.LCD_Init) refers to lcd.o(.bss) for .bss
    lcd.o(i.LCD_Init) refers to delay.o(i.delay_us) for delay_us
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_SSD_BackLightSet) for LCD_SSD_BackLightSet
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_Display_Dir) for LCD_Display_Dir
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_Clear) for LCD_Clear
    lcd.o(i.LCD_ReadPoint) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_ReadPoint) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_ReadPoint) refers to lcd.o(i.LCD_RD_DATA) for LCD_RD_DATA
    lcd.o(i.LCD_ReadPoint) refers to lcd.o(.bss) for .bss
    lcd.o(i.LCD_ReadReg) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_ReadReg) refers to delay.o(i.delay_us) for delay_us
    lcd.o(i.LCD_ReadReg) refers to lcd.o(i.LCD_RD_DATA) for LCD_RD_DATA
    lcd.o(i.LCD_SSD_BackLightSet) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_SSD_BackLightSet) refers to lcd.o(i.LCD_WR_DATA) for LCD_WR_DATA
    lcd.o(i.LCD_SSD_BackLightSet) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    lcd.o(i.LCD_SSD_BackLightSet) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    lcd.o(i.LCD_SSD_BackLightSet) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    lcd.o(i.LCD_Scan_Dir) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.LCD_Scan_Dir) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_Scan_Dir) refers to lcd.o(i.LCD_WR_DATA) for LCD_WR_DATA
    lcd.o(i.LCD_Scan_Dir) refers to lcd.o(.bss) for .bss
    lcd.o(i.LCD_SetCursor) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_SetCursor) refers to lcd.o(i.LCD_WR_DATA) for LCD_WR_DATA
    lcd.o(i.LCD_SetCursor) refers to lcd.o(.bss) for .bss
    lcd.o(i.LCD_Set_Window) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_Set_Window) refers to lcd.o(i.LCD_WR_DATA) for LCD_WR_DATA
    lcd.o(i.LCD_Set_Window) refers to lcd.o(.bss) for .bss
    lcd.o(i.LCD_ShowChar) refers to lcd.o(i.LCD_Fast_DrawPoint) for LCD_Fast_DrawPoint
    lcd.o(i.LCD_ShowChar) refers to lcd.o(.constdata) for .constdata
    lcd.o(i.LCD_ShowChar) refers to lcd.o(.data) for .data
    lcd.o(i.LCD_ShowChar) refers to lcd.o(.bss) for .bss
    lcd.o(i.LCD_ShowNum) refers to lcd.o(i.LCD_Pow) for LCD_Pow
    lcd.o(i.LCD_ShowNum) refers to lcd.o(i.LCD_ShowChar) for LCD_ShowChar
    lcd.o(i.LCD_ShowString) refers to lcd.o(i.LCD_ShowChar) for LCD_ShowChar
    lcd.o(i.LCD_ShowxNum) refers to lcd.o(i.LCD_Pow) for LCD_Pow
    lcd.o(i.LCD_ShowxNum) refers to lcd.o(i.LCD_ShowChar) for LCD_ShowChar
    lcd.o(i.LCD_WriteRAM_Prepare) refers to lcd.o(.bss) for .bss
    pwm.o(i.PWM_GetDuty) refers to pwm.o(.bss) for .bss
    pwm.o(i.PWM_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    pwm.o(i.PWM_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    pwm.o(i.PWM_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    pwm.o(i.PWM_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    pwm.o(i.PWM_Init) refers to pwm.o(.bss) for .bss
    pwm.o(i.PWM_SetDuty) refers to pwm.o(.bss) for .bss
    pwm.o(i.PWM_SetFrequency) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) for HAL_TIM_PWM_Stop
    pwm.o(i.PWM_SetFrequency) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    pwm.o(i.PWM_SetFrequency) refers to pwm.o(.bss) for .bss
    pwm.o(i.PWM_Start) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    pwm.o(i.PWM_Start) refers to pwm.o(.bss) for .bss
    pwm.o(i.PWM_Stop) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) for HAL_TIM_PWM_Stop
    pwm.o(i.PWM_Stop) refers to pwm.o(.bss) for .bss
    ultrasonic.o(i.Ultrasonic_GetDistance) refers to ultrasonic.o(i.Ultrasonic_Trigger) for Ultrasonic_Trigger
    ultrasonic.o(i.Ultrasonic_GetDistance) refers to delay.o(i.delay_us) for delay_us
    ultrasonic.o(i.Ultrasonic_GetDistance) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    ultrasonic.o(i.Ultrasonic_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    ultrasonic.o(i.Ultrasonic_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    ultrasonic.o(i.Ultrasonic_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ultrasonic.o(i.Ultrasonic_Trigger) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ultrasonic.o(i.Ultrasonic_Trigger) refers to delay.o(i.delay_us) for delay_us
    dac.o(i.DAC_GetValue) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_GetValue) for HAL_DAC_GetValue
    dac.o(i.DAC_GetValue) refers to dac.o(.bss) for .bss
    dac.o(i.DAC_GetVoltage) refers to dac.o(i.DAC_GetValue) for DAC_GetValue
    dac.o(i.DAC_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    dac.o(i.DAC_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    dac.o(i.DAC_Init) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_Init) for HAL_DAC_Init
    dac.o(i.DAC_Init) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_ConfigChannel) for HAL_DAC_ConfigChannel
    dac.o(i.DAC_Init) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_Start) for HAL_DAC_Start
    dac.o(i.DAC_Init) refers to dac.o(i.DAC_SetValue) for DAC_SetValue
    dac.o(i.DAC_Init) refers to dac.o(.bss) for .bss
    dac.o(i.DAC_Init) refers to dac.o(.data) for .data
    dac.o(i.DAC_SetValue) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_SetValue) for HAL_DAC_SetValue
    dac.o(i.DAC_SetValue) refers to dac.o(.bss) for .bss
    dac.o(i.DAC_SetVoltage) refers to dac.o(i.DAC_SetValue) for DAC_SetValue
    fan.o(i.Fan_GetDirection) refers to fan.o(.bss) for .bss
    fan.o(i.Fan_GetVoltage) refers to fan.o(.bss) for .bss
    fan.o(i.Fan_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    fan.o(i.Fan_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    fan.o(i.Fan_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    fan.o(i.Fan_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    fan.o(i.Fan_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    fan.o(i.Fan_Init) refers to fan.o(i.Fan_Stop) for Fan_Stop
    fan.o(i.Fan_Init) refers to fan.o(.bss) for .bss
    fan.o(i.Fan_IsRunning) refers to fan.o(.bss) for .bss
    fan.o(i.Fan_SetDirection) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    fan.o(i.Fan_SetDirection) refers to fan.o(.bss) for .bss
    fan.o(i.Fan_SetVoltage) refers to fan.o(.bss) for .bss
    fan.o(i.Fan_Start) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    fan.o(i.Fan_Start) refers to fan.o(i.Fan_SetDirection) for Fan_SetDirection
    fan.o(i.Fan_Start) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    fan.o(i.Fan_Start) refers to fan.o(i.Fan_SetVoltage) for Fan_SetVoltage
    fan.o(i.Fan_Start) refers to fan.o(.bss) for .bss
    fan.o(i.Fan_Stop) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    fan.o(i.Fan_Stop) refers to fan.o(.bss) for .bss
    fan.o(i.Fan_Update) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    fan.o(i.Fan_Update) refers to fan.o(.bss) for .bss
    photoswitch.o(i.PhotoSwitch_ClearFlags) refers to photoswitch.o(.data) for .data
    photoswitch.o(i.PhotoSwitch_ClearFlags) refers to photoswitch.o(.bss) for .bss
    photoswitch.o(i.PhotoSwitch_GetTriggerTime) refers to photoswitch.o(.bss) for .bss
    photoswitch.o(i.PhotoSwitch_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    photoswitch.o(i.PhotoSwitch_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    photoswitch.o(i.PhotoSwitch_Init) refers to photoswitch.o(i.PhotoSwitch_Scan) for PhotoSwitch_Scan
    photoswitch.o(i.PhotoSwitch_Init) refers to photoswitch.o(.data) for .data
    photoswitch.o(i.PhotoSwitch_Init) refers to photoswitch.o(.bss) for .bss
    photoswitch.o(i.PhotoSwitch_IsChanged) refers to photoswitch.o(.bss) for .bss
    photoswitch.o(i.PhotoSwitch_IsReleased) refers to photoswitch.o(.bss) for .bss
    photoswitch.o(i.PhotoSwitch_IsTriggered) refers to photoswitch.o(.bss) for .bss
    photoswitch.o(i.PhotoSwitch_Scan) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    photoswitch.o(i.PhotoSwitch_Scan) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    photoswitch.o(i.PhotoSwitch_Scan) refers to photoswitch.o(.data) for .data
    photoswitch.o(i.PhotoSwitch_Scan) refers to photoswitch.o(.bss) for .bss
    usmart.o(i.TIM4_IRQHandler) refers to usmart.o(.bss) for .bss
    usmart.o(i.TIM4_IRQHandler) refers to usmart_config.o(.data) for usmart_dev
    usmart.o(i.Timer4_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usmart.o(i.Timer4_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usmart.o(i.Timer4_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    usmart.o(i.Timer4_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    usmart.o(i.Timer4_Init) refers to usmart.o(.bss) for .bss
    usmart.o(i.usmart_cmd_rec) refers to usmart_str.o(i.usmart_get_fname) for usmart_get_fname
    usmart.o(i.usmart_cmd_rec) refers to usmart_str.o(i.usmart_strcmp) for usmart_strcmp
    usmart.o(i.usmart_cmd_rec) refers to usmart_str.o(i.usmart_get_fparam) for usmart_get_fparam
    usmart.o(i.usmart_cmd_rec) refers to usmart_config.o(.data) for usmart_dev
    usmart.o(i.usmart_exe) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usmart.o(i.usmart_exe) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    usmart.o(i.usmart_exe) refers to _printf_str.o(.text) for _printf_str
    usmart.o(i.usmart_exe) refers to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    usmart.o(i.usmart_exe) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usmart.o(i.usmart_exe) refers to _printf_dec.o(.text) for _printf_int_dec
    usmart.o(i.usmart_exe) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    usmart.o(i.usmart_exe) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    usmart.o(i.usmart_exe) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    usmart.o(i.usmart_exe) refers to _printf_pad.o(.text) for _printf_pre_padding
    usmart.o(i.usmart_exe) refers to usmart_str.o(i.usmart_get_fname) for usmart_get_fname
    usmart.o(i.usmart_exe) refers to noretval__2printf.o(.text) for __2printf
    usmart.o(i.usmart_exe) refers to usmart_str.o(i.usmart_get_parmpos) for usmart_get_parmpos
    usmart.o(i.usmart_exe) refers to usmart.o(i.usmart_reset_runtime) for usmart_reset_runtime
    usmart.o(i.usmart_exe) refers to usmart.o(i.usmart_get_runtime) for usmart_get_runtime
    usmart.o(i.usmart_exe) refers to usmart_config.o(.data) for usmart_dev
    usmart.o(i.usmart_get_runtime) refers to usmart.o(.bss) for .bss
    usmart.o(i.usmart_get_runtime) refers to usmart_config.o(.data) for usmart_dev
    usmart.o(i.usmart_init) refers to usmart.o(i.Timer4_Init) for Timer4_Init
    usmart.o(i.usmart_init) refers to usmart_config.o(.data) for usmart_dev
    usmart.o(i.usmart_reset_runtime) refers to usmart.o(.bss) for .bss
    usmart.o(i.usmart_reset_runtime) refers to usmart_config.o(.data) for usmart_dev
    usmart.o(i.usmart_scan) refers to usmart.o(i.usmart_sys_cmd_exe) for usmart_sys_cmd_exe
    usmart.o(i.usmart_scan) refers to noretval__2printf.o(.text) for __2printf
    usmart.o(i.usmart_scan) refers to usart.o(.data) for USART_RX_STA
    usmart.o(i.usmart_scan) refers to usart.o(.bss) for USART_RX_BUF
    usmart.o(i.usmart_scan) refers to usmart_config.o(.data) for usmart_dev
    usmart.o(i.usmart_sys_cmd_exe) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usmart.o(i.usmart_sys_cmd_exe) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    usmart.o(i.usmart_sys_cmd_exe) refers to _printf_str.o(.text) for _printf_str
    usmart.o(i.usmart_sys_cmd_exe) refers to _printf_pad.o(.text) for _printf_pre_padding
    usmart.o(i.usmart_sys_cmd_exe) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    usmart.o(i.usmart_sys_cmd_exe) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    usmart.o(i.usmart_sys_cmd_exe) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    usmart.o(i.usmart_sys_cmd_exe) refers to _printf_dec.o(.text) for _printf_int_dec
    usmart.o(i.usmart_sys_cmd_exe) refers to usmart_str.o(i.usmart_get_cmdname) for usmart_get_cmdname
    usmart.o(i.usmart_sys_cmd_exe) refers to usmart_str.o(i.usmart_strcmp) for usmart_strcmp
    usmart.o(i.usmart_sys_cmd_exe) refers to noretval__2printf.o(.text) for __2printf
    usmart.o(i.usmart_sys_cmd_exe) refers to usmart.o(.data) for .data
    usmart.o(i.usmart_sys_cmd_exe) refers to usmart_config.o(.data) for usmart_dev
    usmart.o(i.usmart_sys_cmd_exe) refers to usmart_str.o(i.usmart_get_fname) for usmart_get_fname
    usmart.o(i.usmart_sys_cmd_exe) refers to usmart_str.o(i.usmart_get_aparm) for usmart_get_aparm
    usmart.o(i.usmart_sys_cmd_exe) refers to usmart_str.o(i.usmart_str2num) for usmart_str2num
    usmart.o(.data) refers to usmart.o(.conststring) for .conststring
    usmart_str.o(i.usmart_get_fname) refers to usmart_str.o(i.usmart_strcmp) for usmart_strcmp
    usmart_str.o(i.usmart_get_fname) refers to usmart_str.o(i.usmart_search_nextc) for usmart_search_nextc
    usmart_str.o(i.usmart_get_fparam) refers to usmart_str.o(i.usmart_get_aparm) for usmart_get_aparm
    usmart_str.o(i.usmart_get_fparam) refers to usmart_str.o(i.usmart_str2num) for usmart_str2num
    usmart_str.o(i.usmart_get_fparam) refers to usmart_str.o(i.usmart_get_parmpos) for usmart_get_parmpos
    usmart_str.o(i.usmart_get_fparam) refers to usmart_str.o(i.usmart_strlen) for usmart_strlen
    usmart_str.o(i.usmart_get_fparam) refers to usmart_str.o(i.usmart_strcopy) for usmart_strcopy
    usmart_str.o(i.usmart_get_fparam) refers to usmart_config.o(.data) for usmart_dev
    usmart_str.o(i.usmart_get_parmpos) refers to usmart_config.o(.data) for usmart_dev
    usmart_str.o(i.usmart_str2num) refers to usmart_str.o(i.usmart_pow) for usmart_pow
    usmart_config.o(.data) refers to usmart.o(i.read_addr) for read_addr
    usmart_config.o(.data) refers to usmart_config.o(.conststring) for .conststring
    usmart_config.o(.data) refers to usmart.o(i.write_addr) for write_addr
    usmart_config.o(.data) refers to delay.o(i.delay_ms) for delay_ms
    usmart_config.o(.data) refers to delay.o(i.delay_us) for delay_us
    usmart_config.o(.data) refers to lcd.o(i.LCD_Clear) for LCD_Clear
    usmart_config.o(.data) refers to lcd.o(i.LCD_Fill) for LCD_Fill
    usmart_config.o(.data) refers to lcd.o(i.LCD_DrawLine) for LCD_DrawLine
    usmart_config.o(.data) refers to lcd.o(i.LCD_DrawRectangle) for LCD_DrawRectangle
    usmart_config.o(.data) refers to lcd.o(i.LCD_Draw_Circle) for LCD_Draw_Circle
    usmart_config.o(.data) refers to lcd.o(i.LCD_ShowNum) for LCD_ShowNum
    usmart_config.o(.data) refers to lcd.o(i.LCD_ShowString) for LCD_ShowString
    usmart_config.o(.data) refers to lcd.o(i.LCD_Fast_DrawPoint) for LCD_Fast_DrawPoint
    usmart_config.o(.data) refers to lcd.o(i.LCD_ReadPoint) for LCD_ReadPoint
    usmart_config.o(.data) refers to lcd.o(i.LCD_Display_Dir) for LCD_Display_Dir
    usmart_config.o(.data) refers to lcd.o(i.LCD_ShowxNum) for LCD_ShowxNum
    usmart_config.o(.data) refers to main.o(i.led_set) for led_set
    usmart_config.o(.data) refers to main.o(i.test_fun) for test_fun
    usmart_config.o(.data) refers to usmart_config.o(.data) for usmart_nametab
    usmart_config.o(.data) refers to usmart.o(i.usmart_init) for usmart_init
    usmart_config.o(.data) refers to usmart.o(i.usmart_cmd_rec) for usmart_cmd_rec
    usmart_config.o(.data) refers to usmart.o(i.usmart_exe) for usmart_exe
    usmart_config.o(.data) refers to usmart.o(i.usmart_scan) for usmart_scan
    gesture.o(i.Gesture_GetName) refers to gesture.o(.data) for .data
    gesture.o(i.Gesture_Init) refers to gesture.o(.bss) for .bss
    gesture.o(i.Gesture_Recognize) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    gesture.o(i.Gesture_Recognize) refers to gesture.o(i.Gesture_Reset) for Gesture_Reset
    gesture.o(i.Gesture_Recognize) refers to photoswitch.o(i.PhotoSwitch_IsChanged) for PhotoSwitch_IsChanged
    gesture.o(i.Gesture_Recognize) refers to photoswitch.o(i.PhotoSwitch_IsTriggered) for PhotoSwitch_IsTriggered
    gesture.o(i.Gesture_Recognize) refers to photoswitch.o(i.PhotoSwitch_GetTriggerTime) for PhotoSwitch_GetTriggerTime
    gesture.o(i.Gesture_Recognize) refers to gesture.o(.bss) for .bss
    gesture.o(i.Gesture_Reset) refers to gesture.o(.bss) for .bss
    gesture.o(.data) refers to gesture.o(.conststring) for .conststring
    state_machine.o(i.StateMachine_GetFanState) refers to state_machine.o(.bss) for .bss
    state_machine.o(i.StateMachine_GetState) refers to state_machine.o(.bss) for .bss
    state_machine.o(i.StateMachine_GetStateName) refers to state_machine.o(.data) for .data
    state_machine.o(i.StateMachine_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    state_machine.o(i.StateMachine_Init) refers to state_machine.o(.bss) for .bss
    state_machine.o(i.StateMachine_IsStateChanged) refers to state_machine.o(.bss) for .bss
    state_machine.o(i.StateMachine_Process) refers to ultrasonic.o(i.Ultrasonic_GetDistance) for Ultrasonic_GetDistance
    state_machine.o(i.StateMachine_Process) refers to state_machine.o(i.StateMachine_ProcessFanControl) for StateMachine_ProcessFanControl
    state_machine.o(i.StateMachine_Process) refers to fan.o(i.Fan_SetVoltage) for Fan_SetVoltage
    state_machine.o(i.StateMachine_Process) refers to state_machine.o(i.StateMachine_SetState) for StateMachine_SetState
    state_machine.o(i.StateMachine_Process) refers to state_machine.o(.bss) for .bss
    state_machine.o(i.StateMachine_ProcessFanControl) refers to fan.o(i.Fan_Start) for Fan_Start
    state_machine.o(i.StateMachine_ProcessFanControl) refers to fan.o(i.Fan_Stop) for Fan_Stop
    state_machine.o(i.StateMachine_ProcessFanControl) refers to fan.o(i.Fan_IsRunning) for Fan_IsRunning
    state_machine.o(i.StateMachine_ProcessFanControl) refers to fan.o(i.Fan_GetVoltage) for Fan_GetVoltage
    state_machine.o(i.StateMachine_ProcessFanControl) refers to fan.o(i.Fan_SetVoltage) for Fan_SetVoltage
    state_machine.o(i.StateMachine_ProcessFanControl) refers to state_machine.o(.bss) for .bss
    state_machine.o(i.StateMachine_SetState) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    state_machine.o(i.StateMachine_SetState) refers to state_machine.o(.bss) for .bss
    state_machine.o(i.StateMachine_Update) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    state_machine.o(i.StateMachine_Update) refers to state_machine.o(i.StateMachine_SetState) for StateMachine_SetState
    state_machine.o(i.StateMachine_Update) refers to state_machine.o(.bss) for .bss
    state_machine.o(.data) refers to state_machine.o(.conststring) for .conststring
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to usart.o(.data) for __stdout
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to usart.o(.data) for __stdout
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int.o(.text) for _printf_int_hex
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    dfixu.o(x$fpl$dfixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixu) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixur) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to usart.o(i.fputc) for fputc
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f407xx.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart.o(i._sys_exit) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_exit.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(i.Control_Process), (2 bytes).
    Removing main.o(i.Control_ProcessComboRunning), (2 bytes).
    Removing main.o(i.Control_ProcessComboSetting), (2 bytes).
    Removing main.o(i.Control_ProcessTimeSetting), (2 bytes).
    Removing main.o(i.Control_ProcessVoltageSetting), (2 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (120 bytes).
    Removing stm32f4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_msp.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DeInit), (52 bytes).
    Removing stm32f4xx_hal.o(i.HAL_Delay), (28 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableCompensationCell), (28 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableCompensationCell), (28 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUID), (28 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (84 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Disable), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Enable), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (22 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (22 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (32 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (32 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (84 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (22 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (292 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (28 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (32 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (56 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (88 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (76 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (28 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (72 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (24 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DeInit), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (232 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (140 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (80 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (92 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (340 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (108 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_Init), (128 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_SendBreak), (46 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (46 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (46 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (142 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort), (104 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive), (68 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (92 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (58 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (84 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT), (172 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAPause), (102 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAResume), (94 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop), (88 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DeInit), (40 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive), (186 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (152 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit), (186 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (128 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMAError), (74 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback), (42 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (20 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt), (46 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback), (42 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_EndTxTransfer), (18 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout), (98 bytes).
    Removing stm32f4xx_hal_usart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_usart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_usart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_Abort), (100 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_Abort_IT), (168 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_DMAPause), (38 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_DMAResume), (38 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_DMAStop), (92 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_DeInit), (46 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_GetError), (4 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_GetState), (6 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_IRQHandler), (320 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_Init), (84 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_Receive), (230 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_Receive_DMA), (200 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_Receive_IT), (80 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_RxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_Transmit), (186 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive), (274 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA), (220 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive_IT), (102 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_Transmit_DMA), (128 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_Transmit_IT), (62 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_TxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_TxRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_DMAAbortOnError), (16 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_DMAError), (80 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_DMAReceiveCplt), (100 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_DMARxAbortCallback), (38 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_DMARxHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_DMATransmitCplt), (62 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_DMATxAbortCallback), (38 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_DMATxHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_EndRxTransfer), (28 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_EndTxTransfer), (18 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_Receive_IT), (176 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_SetConfig), (260 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_TransmitReceive_IT), (232 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_Transmit_IT), (98 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_WaitOnFlagUntilTimeout), (114 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift), (40 bytes).
    Removing stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam), (84 bytes).
    Removing stm32f4xx_hal_dma.o(i.DMA_SetConfig), (40 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Abort), (142 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit), (86 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler), (424 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Init), (212 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (284 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (80 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start), (70 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT), (120 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (96 bytes).
    Removing stm32f4xx_hal_dma.o(.constdata), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (30 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (14 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (100 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (2452 bytes).
    Removing stm32f4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start), (26 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop), (44 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (48 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (42 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource), (240 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (174 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (260 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (96 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (260 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (96 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init), (150 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start), (50 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (272 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (68 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (116 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (116 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (284 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init), (54 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start), (26 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (200 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop), (44 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (100 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (94 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler), (358 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init), (54 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (240 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (108 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop), (88 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (144 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (136 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (218 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (74 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (60 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (80 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (100 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (240 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (108 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (144 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (136 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (68 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchronization), (66 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchronization_IT), (66 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt), (62 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (62 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAError), (16 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (16 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt), (16 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig), (20 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig), (16 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage), (34 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage), (36 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig), (54 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutationCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime), (90 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutationEvent), (92 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutationEvent_DMA), (124 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutationEvent_IT), (102 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (56 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (176 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (28 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (38 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (46 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (56 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (56 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization), (80 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (36 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (220 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (96 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (68 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (124 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (134 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (26 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (46 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (68 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (88 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (36 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (220 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (96 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (68 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (124 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (134 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (34 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (22 bytes).
    Removing stm32f4xx_hal_spi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_spi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_spi.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Abort), (192 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT), (232 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_DMAPause), (38 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_DMAResume), (38 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_DMAStop), (40 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_DeInit), (46 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_GetError), (4 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_GetState), (6 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler), (212 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Init), (140 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_MspInit), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Receive), (490 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA), (244 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT), (196 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit), (404 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive), (608 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA), (292 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT), (196 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA), (212 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_IT), (180 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT), (64 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BITCRC), (34 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT), (64 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BITCRC), (44 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT), (74 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT), (72 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_AbortRx_ISR), (80 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_AbortTx_ISR), (22 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_CheckFlag_BSY), (32 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR), (176 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR), (104 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR), (124 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAAbortOnError), (16 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAError), (34 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt), (10 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt), (10 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt), (10 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt), (160 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback), (70 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt), (90 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt), (152 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMATxAbortCallback), (104 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_RxISR_16BIT), (72 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_RxISR_16BITCRC), (34 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_RxISR_8BIT), (72 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_RxISR_8BITCRC), (26 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_TxISR_16BIT), (50 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_TxISR_8BIT), (48 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout), (150 bytes).
    Removing stm32f4xx_hal_sram.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_sram.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_sram.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_DeInit), (28 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_GetState), (6 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_MspInit), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_16b), (54 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_32b), (50 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_8b), (54 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA), (60 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Disable), (48 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Enable), (42 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_16b), (66 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_32b), (62 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_8b), (66 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA), (80 bytes).
    Removing stm32f4xx_ll_fsmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_ll_fsmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_ll_fsmc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NAND_AttributeSpace_Timing_Init), (48 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NAND_CommonSpace_Timing_Init), (48 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NAND_DeInit), (52 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NAND_ECC_Disable), (26 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NAND_ECC_Enable), (26 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NAND_GetECC), (78 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NAND_Init), (80 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_DeInit), (48 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_WriteOperation_Disable), (16 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_WriteOperation_Enable), (16 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_PCCARD_AttributeSpace_Timing_Init), (28 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_PCCARD_CommonSpace_Timing_Init), (28 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_PCCARD_DeInit), (30 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_PCCARD_IOSpace_Timing_Init), (28 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_PCCARD_Init), (40 bytes).
    Removing stm32f4xx_hal_dac.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dac.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dac.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dac.o(i.DAC_DMAConvCpltCh1), (16 bytes).
    Removing stm32f4xx_hal_dac.o(i.DAC_DMAErrorCh1), (24 bytes).
    Removing stm32f4xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1), (10 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_ConvCpltCallbackCh1), (2 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_ConvHalfCpltCallbackCh1), (2 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_DMAUnderrunCallbackCh1), (2 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_DeInit), (30 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_ErrorCallbackCh1), (2 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_GetError), (4 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_GetState), (4 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_GetValue), (12 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_IRQHandler), (92 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA), (256 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_Stop), (22 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_Stop_DMA), (56 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2), (16 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.DAC_DMAErrorCh2), (24 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2), (10 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ConvCpltCallbackCh2), (2 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ConvHalfCpltCallbackCh2), (2 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_DMAUnderrunCallbackCh2), (2 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_DualGetValue), (12 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_DualSetValue), (24 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ErrorCallbackCh2), (2 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_NoiseWaveGenerate), (52 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_TriangleWaveGenerate), (52 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing delay.o(.rrx_text), (6 bytes).
    Removing sys.o(.rev16_text), (4 bytes).
    Removing sys.o(.revsh_text), (4 bytes).
    Removing sys.o(.rrx_text), (6 bytes).
    Removing sys.o(.emb_text), (16 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing led.o(.rrx_text), (6 bytes).
    Removing lcd.o(.rev16_text), (4 bytes).
    Removing lcd.o(.revsh_text), (4 bytes).
    Removing lcd.o(.rrx_text), (6 bytes).
    Removing lcd.o(i.LCD_BGR2RGB), (18 bytes).
    Removing lcd.o(i.LCD_Color_Fill), (86 bytes).
    Removing lcd.o(i.LCD_DisplayOff), (28 bytes).
    Removing lcd.o(i.LCD_DisplayOn), (28 bytes).
    Removing lcd.o(i.LCD_ReadReg), (20 bytes).
    Removing lcd.o(i.LCD_Set_Window), (340 bytes).
    Removing lcd.o(i.LCD_WriteRAM), (10 bytes).
    Removing lcd.o(i.opt_delay), (8 bytes).
    Removing pwm.o(.rev16_text), (4 bytes).
    Removing pwm.o(.revsh_text), (4 bytes).
    Removing pwm.o(.rrx_text), (6 bytes).
    Removing pwm.o(i.PWM_GetDuty), (16 bytes).
    Removing pwm.o(i.PWM_Init), (152 bytes).
    Removing pwm.o(i.PWM_SetDuty), (24 bytes).
    Removing pwm.o(i.PWM_SetFrequency), (52 bytes).
    Removing pwm.o(i.PWM_Start), (12 bytes).
    Removing pwm.o(i.PWM_Stop), (12 bytes).
    Removing pwm.o(.bss), (88 bytes).
    Removing ultrasonic.o(.rev16_text), (4 bytes).
    Removing ultrasonic.o(.revsh_text), (4 bytes).
    Removing ultrasonic.o(.rrx_text), (6 bytes).
    Removing dac.o(.rev16_text), (4 bytes).
    Removing dac.o(.revsh_text), (4 bytes).
    Removing dac.o(.rrx_text), (6 bytes).
    Removing dac.o(i.DAC_GetValue), (20 bytes).
    Removing dac.o(i.DAC_GetVoltage), (40 bytes).
    Removing fan.o(.rev16_text), (4 bytes).
    Removing fan.o(.revsh_text), (4 bytes).
    Removing fan.o(.rrx_text), (6 bytes).
    Removing photoswitch.o(.rev16_text), (4 bytes).
    Removing photoswitch.o(.revsh_text), (4 bytes).
    Removing photoswitch.o(.rrx_text), (6 bytes).
    Removing photoswitch.o(i.PhotoSwitch_ClearFlags), (36 bytes).
    Removing photoswitch.o(i.PhotoSwitch_IsReleased), (20 bytes).
    Removing usmart.o(.rev16_text), (4 bytes).
    Removing usmart.o(.revsh_text), (4 bytes).
    Removing usmart.o(.rrx_text), (6 bytes).
    Removing usmart_str.o(.rev16_text), (4 bytes).
    Removing usmart_str.o(.revsh_text), (4 bytes).
    Removing usmart_str.o(.rrx_text), (6 bytes).
    Removing usmart_config.o(.rev16_text), (4 bytes).
    Removing usmart_config.o(.revsh_text), (4 bytes).
    Removing usmart_config.o(.rrx_text), (6 bytes).
    Removing gesture.o(.rev16_text), (4 bytes).
    Removing gesture.o(.revsh_text), (4 bytes).
    Removing gesture.o(.rrx_text), (6 bytes).
    Removing gesture.o(i.Gesture_IsValid), (14 bytes).
    Removing state_machine.o(.rev16_text), (4 bytes).
    Removing state_machine.o(.revsh_text), (4 bytes).
    Removing state_machine.o(.rrx_text), (6 bytes).
    Removing state_machine.o(i.StateMachine_GetFanState), (12 bytes).
    Removing state_machine.o(i.StateMachine_IsStateChanged), (16 bytes).

561 unused section(s) (total 32848 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/dczerorl2.s                0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/dfixu.s                         0x00000000   Number         0  dfixu.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ..\APP\GESTURE\gesture.c                 0x00000000   Number         0  gesture.o ABSOLUTE
    ..\APP\STATE_MACHINE\state_machine.c     0x00000000   Number         0  state_machine.o ABSOLUTE
    ..\CORE\startup_stm32f407xx.s            0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    ..\HALLIB\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\HALLIB\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\HALLIB\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dac.c 0x00000000   Number         0  stm32f4xx_hal_dac.o ABSOLUTE
    ..\HALLIB\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dac_ex.c 0x00000000   Number         0  stm32f4xx_hal_dac_ex.o ABSOLUTE
    ..\HALLIB\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\HALLIB\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\HALLIB\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\HALLIB\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\HALLIB\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\HALLIB\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\HALLIB\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\HALLIB\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_spi.c 0x00000000   Number         0  stm32f4xx_hal_spi.o ABSOLUTE
    ..\HALLIB\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_sram.c 0x00000000   Number         0  stm32f4xx_hal_sram.o ABSOLUTE
    ..\HALLIB\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ..\HALLIB\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ..\HALLIB\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\HALLIB\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_usart.c 0x00000000   Number         0  stm32f4xx_hal_usart.o ABSOLUTE
    ..\HALLIB\STM32F4xx_HAL_Driver\Src\stm32f4xx_ll_fsmc.c 0x00000000   Number         0  stm32f4xx_ll_fsmc.o ABSOLUTE
    ..\HARDWARE\DAC\dac.c                    0x00000000   Number         0  dac.o ABSOLUTE
    ..\HARDWARE\FAN\fan.c                    0x00000000   Number         0  fan.o ABSOLUTE
    ..\HARDWARE\LCD\lcd.c                    0x00000000   Number         0  lcd.o ABSOLUTE
    ..\HARDWARE\LED\led.c                    0x00000000   Number         0  led.o ABSOLUTE
    ..\HARDWARE\PHOTOSWITCH\photoswitch.c    0x00000000   Number         0  photoswitch.o ABSOLUTE
    ..\HARDWARE\PWM\pwm.c                    0x00000000   Number         0  pwm.o ABSOLUTE
    ..\HARDWARE\ULTRASONIC\ultrasonic.c      0x00000000   Number         0  ultrasonic.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\USMART\usmart.c                       0x00000000   Number         0  usmart.o ABSOLUTE
    ..\USMART\usmart_config.c                0x00000000   Number         0  usmart_config.o ABSOLUTE
    ..\USMART\usmart_str.c                   0x00000000   Number         0  usmart_str.o ABSOLUTE
    ..\\APP\\GESTURE\\gesture.c              0x00000000   Number         0  gesture.o ABSOLUTE
    ..\\APP\\STATE_MACHINE\\state_machine.c  0x00000000   Number         0  state_machine.o ABSOLUTE
    ..\\HALLIB\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\\HALLIB\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\\HALLIB\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dac.c 0x00000000   Number         0  stm32f4xx_hal_dac.o ABSOLUTE
    ..\\HALLIB\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dac_ex.c 0x00000000   Number         0  stm32f4xx_hal_dac_ex.o ABSOLUTE
    ..\\HALLIB\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\\HALLIB\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\\HALLIB\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\\HALLIB\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\\HALLIB\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\\HALLIB\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\\HALLIB\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\\HALLIB\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_spi.c 0x00000000   Number         0  stm32f4xx_hal_spi.o ABSOLUTE
    ..\\HALLIB\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_sram.c 0x00000000   Number         0  stm32f4xx_hal_sram.o ABSOLUTE
    ..\\HALLIB\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ..\\HALLIB\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ..\\HALLIB\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\\HALLIB\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_usart.c 0x00000000   Number         0  stm32f4xx_hal_usart.o ABSOLUTE
    ..\\HALLIB\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_ll_fsmc.c 0x00000000   Number         0  stm32f4xx_ll_fsmc.o ABSOLUTE
    ..\\HARDWARE\\DAC\\dac.c                 0x00000000   Number         0  dac.o ABSOLUTE
    ..\\HARDWARE\\FAN\\fan.c                 0x00000000   Number         0  fan.o ABSOLUTE
    ..\\HARDWARE\\LCD\\lcd.c                 0x00000000   Number         0  lcd.o ABSOLUTE
    ..\\HARDWARE\\LED\\led.c                 0x00000000   Number         0  led.o ABSOLUTE
    ..\\HARDWARE\\PHOTOSWITCH\\photoswitch.c 0x00000000   Number         0  photoswitch.o ABSOLUTE
    ..\\HARDWARE\\PWM\\pwm.c                 0x00000000   Number         0  pwm.o ABSOLUTE
    ..\\HARDWARE\\ULTRASONIC\\ultrasonic.c   0x00000000   Number         0  ultrasonic.o ABSOLUTE
    ..\\SYSTEM\\delay\\delay.c               0x00000000   Number         0  delay.o ABSOLUTE
    ..\\SYSTEM\\sys\\sys.c                   0x00000000   Number         0  sys.o ABSOLUTE
    ..\\SYSTEM\\usart\\usart.c               0x00000000   Number         0  usart.o ABSOLUTE
    ..\\USMART\\usmart.c                     0x00000000   Number         0  usmart.o ABSOLUTE
    ..\\USMART\\usmart_config.c              0x00000000   Number         0  usmart_config.o ABSOLUTE
    ..\\USMART\\usmart_str.c                 0x00000000   Number         0  usmart_str.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32f4xx_hal_msp.c                      0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    stm32f4xx_hal_msp.c                      0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!dczerorl2                              0x080001c4   Section       90  __dczerorl2.o(!!dczerorl2)
    !!handler_zi                             0x08000220   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x0800023c   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000003  0x0800023c   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000009  0x08000242   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x08000248   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000C  0x0800024e   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$00000013  0x08000254   Section        6  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    .ARM.Collect$$_printf_percent$$00000014  0x0800025a   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000017  0x08000260   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x08000264   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x08000266   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x0800026a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x0800026a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x0800026a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x0800026a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x0800026a   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x08000270   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08000270   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000270   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x08000270   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x0800027a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x0800027a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x0800027a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x0800027a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x0800027a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x0800027a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x0800027a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x0800027a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x0800027a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x0800027a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x0800027a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x0800027a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x0800027a   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x0800027c   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x0800027e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x0800027e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x0800027e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x0800027e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x0800027e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x0800027e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x0800027e   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x08000280   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000280   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000280   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x08000286   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x08000286   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x0800028a   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x0800028a   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000292   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000294   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000294   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000298   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x080002a0   Section       64  startup_stm32f407xx.o(.text)
    $v0                                      0x080002a0   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x080002e0   Section        2  use_no_semi_2.o(.text)
    .text                                    0x080002e4   Section        0  noretval__2printf.o(.text)
    .text                                    0x080002fc   Section        0  noretval__2sprintf.o(.text)
    .text                                    0x08000324   Section        0  _printf_pad.o(.text)
    .text                                    0x08000372   Section        0  _printf_str.o(.text)
    .text                                    0x080003c4   Section        0  _printf_dec.o(.text)
    .text                                    0x0800043c   Section        0  _printf_hex_int.o(.text)
    .text                                    0x08000494   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x0800061c   Section       78  rt_memclr_w.o(.text)
    .text                                    0x0800066a   Section        0  heapauxi.o(.text)
    .text                                    0x08000670   Section        2  use_no_semi.o(.text)
    .text                                    0x08000672   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08000724   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x08000727   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08000b44   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08000b45   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000b74   Section        0  _sputc.o(.text)
    .text                                    0x08000b7e   Section        0  _printf_char.o(.text)
    .text                                    0x08000bac   Section        0  _printf_char_file.o(.text)
    .text                                    0x08000bd0   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x08000bd8   Section      138  lludiv10.o(.text)
    .text                                    0x08000c64   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x08000ce4   Section        0  bigflt0.o(.text)
    .text                                    0x08000dc8   Section        0  ferror.o(.text)
    .text                                    0x08000dd0   Section        8  libspace.o(.text)
    .text                                    0x08000dd8   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08000e22   Section        0  exit.o(.text)
    .text                                    0x08000e34   Section      128  strcmpv7m.o(.text)
    CL$$btod_d2e                             0x08000eb4   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x08000ef2   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x08000f38   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x08000f98   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x080012d0   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x080013ac   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x080013d6   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x08001400   Section      580  btod.o(CL$$btod_mult_common)
    i.BusFault_Handler                       0x08001644   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.DAC_Init                               0x08001648   Section        0  dac.o(i.DAC_Init)
    i.DAC_SetValue                           0x080016d8   Section        0  dac.o(i.DAC_SetValue)
    i.DAC_SetVoltage                         0x080016f4   Section        0  dac.o(i.DAC_SetVoltage)
    i.DebugMon_Handler                       0x08001740   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.Display_Init                           0x08001744   Section        0  main.o(i.Display_Init)
    i.Display_Update                         0x08001908   Section        0  main.o(i.Display_Update)
    i.FSMC_NORSRAM_Extended_Timing_Init      0x08001d70   Section        0  stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Extended_Timing_Init)
    i.FSMC_NORSRAM_Init                      0x08001db0   Section        0  stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Init)
    i.FSMC_NORSRAM_Timing_Init               0x08001e04   Section        0  stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Timing_Init)
    i.Fan_GetDirection                       0x08001e48   Section        0  fan.o(i.Fan_GetDirection)
    i.Fan_GetVoltage                         0x08001e54   Section        0  fan.o(i.Fan_GetVoltage)
    i.Fan_Init                               0x08001e60   Section        0  fan.o(i.Fan_Init)
    i.Fan_IsRunning                          0x08001f60   Section        0  fan.o(i.Fan_IsRunning)
    i.Fan_SetDirection                       0x08001f6c   Section        0  fan.o(i.Fan_SetDirection)
    i.Fan_SetVoltage                         0x08001fb4   Section        0  fan.o(i.Fan_SetVoltage)
    i.Fan_Start                              0x08001fe8   Section        0  fan.o(i.Fan_Start)
    i.Fan_Stop                               0x0800201c   Section        0  fan.o(i.Fan_Stop)
    i.Fan_Update                             0x08002060   Section        0  fan.o(i.Fan_Update)
    i.Gesture_GetName                        0x0800207c   Section        0  gesture.o(i.Gesture_GetName)
    i.Gesture_Init                           0x08002098   Section        0  gesture.o(i.Gesture_Init)
    i.Gesture_Recognize                      0x080020b0   Section        0  gesture.o(i.Gesture_Recognize)
    i.Gesture_Reset                          0x08002184   Section        0  gesture.o(i.Gesture_Reset)
    i.HAL_DAC_ConfigChannel                  0x0800219c   Section        0  stm32f4xx_hal_dac.o(i.HAL_DAC_ConfigChannel)
    i.HAL_DAC_Init                           0x080021de   Section        0  stm32f4xx_hal_dac.o(i.HAL_DAC_Init)
    i.HAL_DAC_MspInit                        0x08002206   Section        0  stm32f4xx_hal_dac.o(i.HAL_DAC_MspInit)
    i.HAL_DAC_SetValue                       0x08002208   Section        0  stm32f4xx_hal_dac.o(i.HAL_DAC_SetValue)
    i.HAL_DAC_Start                          0x08002222   Section        0  stm32f4xx_hal_dac.o(i.HAL_DAC_Start)
    i.HAL_DMA_Abort_IT                       0x0800228e   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_GPIO_Init                          0x080022b4   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x08002474   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GPIO_WritePin                      0x0800247e   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetREVID                           0x08002488   Section        0  stm32f4xx_hal.o(i.HAL_GetREVID)
    i.HAL_GetTick                            0x08002494   Section        0  stm32f4xx_hal.o(i.HAL_GetTick)
    i.HAL_IncTick                            0x080024a0   Section        0  stm32f4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x080024b0   Section        0  stm32f4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x080024dc   Section        0  stm32f4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08002504   Section        0  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08002506   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x0800251c   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x0800255c   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x08002580   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetPCLK1Freq                   0x080026f4   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08002724   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08002754   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x080027c4   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SRAM_Init                          0x08002ad8   Section        0  stm32f4xx_hal_sram.o(i.HAL_SRAM_Init)
    i.HAL_SRAM_MspInit                       0x08002b30   Section        0  lcd.o(i.HAL_SRAM_MspInit)
    i.HAL_SYSTICK_CLKSourceConfig            0x08002be8   Section        0  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig)
    i.HAL_SYSTICK_Config                     0x08002c00   Section        0  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIM_Base_Init                      0x08002c28   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x08002c5e   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Base_Start_IT                  0x08002c60   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    i.HAL_TIM_PWM_ConfigChannel              0x08002c78   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    i.HAL_TIM_PWM_Init                       0x08002d48   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    i.HAL_TIM_PWM_MspInit                    0x08002d7e   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    i.HAL_TIM_PWM_Start                      0x08002d80   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    i.HAL_UART_ErrorCallback                 0x08002db8   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_GetState                      0x08002dba   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_GetState)
    i.HAL_UART_IRQHandler                    0x08002dc4   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x08002ed4   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08002f38   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Receive_IT                    0x08002fb4   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    i.HAL_UART_RxCpltCallback                0x08002ffc   Section        0  usart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_TxCpltCallback                0x08003054   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x08003056   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.LCD_Clear                              0x08003058   Section        0  lcd.o(i.LCD_Clear)
    i.LCD_Display_Dir                        0x0800308c   Section        0  lcd.o(i.LCD_Display_Dir)
    i.LCD_DrawLine                           0x08003174   Section        0  lcd.o(i.LCD_DrawLine)
    i.LCD_DrawPoint                          0x0800320c   Section        0  lcd.o(i.LCD_DrawPoint)
    i.LCD_DrawRectangle                      0x08003228   Section        0  lcd.o(i.LCD_DrawRectangle)
    i.LCD_Draw_Circle                        0x08003260   Section        0  lcd.o(i.LCD_Draw_Circle)
    i.LCD_Fast_DrawPoint                     0x08003310   Section        0  lcd.o(i.LCD_Fast_DrawPoint)
    i.LCD_Fill                               0x080033e0   Section        0  lcd.o(i.LCD_Fill)
    i.LCD_Init                               0x08003428   Section        0  lcd.o(i.LCD_Init)
    i.LCD_Pow                                0x08005f70   Section        0  lcd.o(i.LCD_Pow)
    i.LCD_RD_DATA                            0x08005f80   Section        0  lcd.o(i.LCD_RD_DATA)
    i.LCD_ReadPoint                          0x08005f94   Section        0  lcd.o(i.LCD_ReadPoint)
    i.LCD_SSD_BackLightSet                   0x08006018   Section        0  lcd.o(i.LCD_SSD_BackLightSet)
    i.LCD_Scan_Dir                           0x08006068   Section        0  lcd.o(i.LCD_Scan_Dir)
    i.LCD_SetCursor                          0x08006210   Section        0  lcd.o(i.LCD_SetCursor)
    i.LCD_ShowChar                           0x08006300   Section        0  lcd.o(i.LCD_ShowChar)
    i.LCD_ShowNum                            0x080063dc   Section        0  lcd.o(i.LCD_ShowNum)
    i.LCD_ShowString                         0x08006452   Section        0  lcd.o(i.LCD_ShowString)
    i.LCD_ShowxNum                           0x080064a6   Section        0  lcd.o(i.LCD_ShowxNum)
    i.LCD_WR_DATA                            0x0800652c   Section        0  lcd.o(i.LCD_WR_DATA)
    i.LCD_WR_REG                             0x08006544   Section        0  lcd.o(i.LCD_WR_REG)
    i.LCD_WriteRAM_Prepare                   0x0800655c   Section        0  lcd.o(i.LCD_WriteRAM_Prepare)
    i.LCD_WriteReg                           0x08006570   Section        0  lcd.o(i.LCD_WriteReg)
    i.LED_Init                               0x0800657c   Section        0  led.o(i.LED_Init)
    i.MemManage_Handler                      0x080065cc   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x080065ce   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.NVIC_SetPriority                       0x080065d0   Section        0  stm32f4xx_hal_cortex.o(i.NVIC_SetPriority)
    NVIC_SetPriority                         0x080065d1   Thumb Code    32  stm32f4xx_hal_cortex.o(i.NVIC_SetPriority)
    i.PendSV_Handler                         0x080065f0   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.PhotoSwitch_GetTriggerTime             0x080065f4   Section        0  photoswitch.o(i.PhotoSwitch_GetTriggerTime)
    i.PhotoSwitch_Init                       0x0800660c   Section        0  photoswitch.o(i.PhotoSwitch_Init)
    i.PhotoSwitch_IsChanged                  0x0800669c   Section        0  photoswitch.o(i.PhotoSwitch_IsChanged)
    i.PhotoSwitch_IsTriggered                0x080066b0   Section        0  photoswitch.o(i.PhotoSwitch_IsTriggered)
    i.PhotoSwitch_Scan                       0x080066c8   Section        0  photoswitch.o(i.PhotoSwitch_Scan)
    i.SVC_Handler                            0x08006748   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.StateMachine_GetState                  0x0800674c   Section        0  state_machine.o(i.StateMachine_GetState)
    i.StateMachine_GetStateName              0x08006758   Section        0  state_machine.o(i.StateMachine_GetStateName)
    i.StateMachine_Init                      0x08006774   Section        0  state_machine.o(i.StateMachine_Init)
    i.StateMachine_Process                   0x08006790   Section        0  state_machine.o(i.StateMachine_Process)
    i.StateMachine_ProcessFanControl         0x08006850   Section        0  state_machine.o(i.StateMachine_ProcessFanControl)
    StateMachine_ProcessFanControl           0x08006851   Thumb Code   104  state_machine.o(i.StateMachine_ProcessFanControl)
    i.StateMachine_SetState                  0x080068bc   Section        0  state_machine.o(i.StateMachine_SetState)
    i.StateMachine_Update                    0x080068fc   Section        0  state_machine.o(i.StateMachine_Update)
    i.Stm32_Clock_Init                       0x08006924   Section        0  sys.o(i.Stm32_Clock_Init)
    i.SysTick_Handler                        0x080069b8   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemInit                             0x080069bc   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.System_Init                            0x08006a10   Section        0  main.o(i.System_Init)
    i.TIM4_IRQHandler                        0x08006aa0   Section        0  usmart.o(i.TIM4_IRQHandler)
    i.TIM_Base_SetConfig                     0x08006ad4   Section        0  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_CCxChannelCmd                      0x08006b94   Section        0  stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    i.TIM_OC1_SetConfig                      0x08006bac   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    TIM_OC1_SetConfig                        0x08006bad   Thumb Code    80  stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    i.TIM_OC2_SetConfig                      0x08006c04   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    i.TIM_OC3_SetConfig                      0x08006c68   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    TIM_OC3_SetConfig                        0x08006c69   Thumb Code    88  stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    i.TIM_OC4_SetConfig                      0x08006cc8   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    TIM_OC4_SetConfig                        0x08006cc9   Thumb Code    70  stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    i.Timer4_Init                            0x08006d18   Section        0  usmart.o(i.Timer4_Init)
    i.UART_DMAAbortOnError                   0x08006d6c   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x08006d6d   Thumb Code    16  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_EndRxTransfer                     0x08006d7c   Section        0  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08006d7d   Thumb Code    28  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_Receive_IT                        0x08006d98   Section        0  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x08006d99   Thumb Code   140  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x08006e24   Section        0  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x08006e25   Thumb Code   676  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Transmit_IT                       0x080070d0   Section        0  stm32f4xx_hal_uart.o(i.UART_Transmit_IT)
    UART_Transmit_IT                         0x080070d1   Thumb Code    98  stm32f4xx_hal_uart.o(i.UART_Transmit_IT)
    i.USART1_IRQHandler                      0x08007134   Section        0  usart.o(i.USART1_IRQHandler)
    i.Ultrasonic_GetDistance                 0x08007160   Section        0  ultrasonic.o(i.Ultrasonic_GetDistance)
    i.Ultrasonic_Init                        0x080071d0   Section        0  ultrasonic.o(i.Ultrasonic_Init)
    i.Ultrasonic_IsInRange                   0x08007230   Section        0  ultrasonic.o(i.Ultrasonic_IsInRange)
    i.Ultrasonic_Trigger                     0x08007248   Section        0  ultrasonic.o(i.Ultrasonic_Trigger)
    i.UsageFault_Handler                     0x08007270   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.__ARM_fpclassify                       0x08007272   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i._is_digit                              0x080072a2   Section        0  __printf_wp.o(i._is_digit)
    i._sys_exit                              0x080072b0   Section        0  usart.o(i._sys_exit)
    i.delay_init                             0x080072b4   Section        0  delay.o(i.delay_init)
    i.delay_ms                               0x080072c8   Section        0  delay.o(i.delay_ms)
    i.delay_us                               0x080072e4   Section        0  delay.o(i.delay_us)
    i.fputc                                  0x08007318   Section        0  usart.o(i.fputc)
    i.led_set                                0x0800732c   Section        0  main.o(i.led_set)
    i.main                                   0x08007338   Section        0  main.o(i.main)
    i.read_addr                              0x08007414   Section        0  usmart.o(i.read_addr)
    i.test_fun                               0x08007418   Section        0  main.o(i.test_fun)
    i.uart_init                              0x080074f0   Section        0  usart.o(i.uart_init)
    i.usmart_cmd_rec                         0x08007528   Section        0  usmart.o(i.usmart_cmd_rec)
    i.usmart_exe                             0x080075bc   Section        0  usmart.o(i.usmart_exe)
    i.usmart_get_aparm                       0x0800781c   Section        0  usmart_str.o(i.usmart_get_aparm)
    i.usmart_get_cmdname                     0x080078c0   Section        0  usmart_str.o(i.usmart_get_cmdname)
    i.usmart_get_fname                       0x080078f0   Section        0  usmart_str.o(i.usmart_get_fname)
    i.usmart_get_fparam                      0x08007a58   Section        0  usmart_str.o(i.usmart_get_fparam)
    i.usmart_get_parmpos                     0x08007b28   Section        0  usmart_str.o(i.usmart_get_parmpos)
    i.usmart_get_runtime                     0x08007b4c   Section        0  usmart.o(i.usmart_get_runtime)
    i.usmart_init                            0x08007b7c   Section        0  usmart.o(i.usmart_init)
    i.usmart_pow                             0x08007ba4   Section        0  usmart_str.o(i.usmart_pow)
    i.usmart_reset_runtime                   0x08007bb4   Section        0  usmart.o(i.usmart_reset_runtime)
    i.usmart_scan                            0x08007bdc   Section        0  usmart.o(i.usmart_scan)
    i.usmart_search_nextc                    0x08007c80   Section        0  usmart_str.o(i.usmart_search_nextc)
    i.usmart_str2num                         0x08007c90   Section        0  usmart_str.o(i.usmart_str2num)
    i.usmart_strcmp                          0x08007d66   Section        0  usmart_str.o(i.usmart_strcmp)
    i.usmart_strcopy                         0x08007d7e   Section        0  usmart_str.o(i.usmart_strcopy)
    i.usmart_strlen                          0x08007d90   Section        0  usmart_str.o(i.usmart_strlen)
    i.usmart_sys_cmd_exe                     0x08007da4   Section        0  usmart.o(i.usmart_sys_cmd_exe)
    i.write_addr                             0x08008424   Section        0  usmart.o(i.write_addr)
    locale$$code                             0x08008428   Section       44  lc_numeric_c.o(locale$$code)
    x$fpl$dfixu                              0x08008454   Section       90  dfixu.o(x$fpl$dfixu)
    $v0                                      0x08008454   Number         0  dfixu.o(x$fpl$dfixu)
    x$fpl$dfltu                              0x080084ae   Section       38  dflt_clz.o(x$fpl$dfltu)
    $v0                                      0x080084ae   Number         0  dflt_clz.o(x$fpl$dfltu)
    x$fpl$dmul                               0x080084d4   Section      340  dmul.o(x$fpl$dmul)
    $v0                                      0x080084d4   Number         0  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x08008628   Section      156  dnaninf.o(x$fpl$dnaninf)
    $v0                                      0x08008628   Number         0  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x080086c4   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x080086c4   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$f2d                                0x080086d0   Section       86  f2d.o(x$fpl$f2d)
    $v0                                      0x080086d0   Number         0  f2d.o(x$fpl$f2d)
    x$fpl$fnaninf                            0x08008726   Section      140  fnaninf.o(x$fpl$fnaninf)
    $v0                                      0x08008726   Number         0  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fpinit                             0x080087b2   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x080087b2   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$printf1                            0x080087bc   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x080087bc   Number         0  printf1.o(x$fpl$printf1)
    .constdata                               0x080087c0   Section       16  system_stm32f4xx.o(.constdata)
    x$fpl$usenofp                            0x080087c0   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x080087d0   Section        8  system_stm32f4xx.o(.constdata)
    .constdata                               0x080087d8   Section    18240  lcd.o(.constdata)
    .constdata                               0x0800cf18   Section       40  _printf_hex_int.o(.constdata)
    uc_hextab                                0x0800cf18   Data          20  _printf_hex_int.o(.constdata)
    lc_hextab                                0x0800cf2c   Data          20  _printf_hex_int.o(.constdata)
    .constdata                               0x0800cf40   Section       17  __printf_flags_ss_wp.o(.constdata)
    maptable                                 0x0800cf40   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x0800cf54   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x0800cf54   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x0800cf90   Data          64  bigflt0.o(.constdata)
    .conststring                             0x0800cfe8   Section       40  usmart.o(.conststring)
    .conststring                             0x0800d010   Section      711  usmart_config.o(.conststring)
    .conststring                             0x0800d2d8   Section       69  gesture.o(.conststring)
    .conststring                             0x0800d320   Section       84  state_machine.o(.conststring)
    locale$$data                             0x0800d394   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x0800d398   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x0800d3a0   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x0800d3ac   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x0800d3ae   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x0800d3af   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_end                            0x0800d3b0   Data           0  lc_numeric_c.o(locale$$data)
    .data                                    0x20000000   Section       16  main.o(.data)
    .data                                    0x20000010   Section        4  system_stm32f4xx.o(.data)
    .data                                    0x20000014   Section        4  stm32f4xx_hal.o(.data)
    .data                                    0x20000018   Section        4  delay.o(.data)
    fac_us                                   0x20000018   Data           4  delay.o(.data)
    .data                                    0x2000001c   Section        4  usart.o(.data)
    .data                                    0x20000020   Section        4  usart.o(.data)
    .data                                    0x20000024   Section        8  lcd.o(.data)
    .data                                    0x2000002c   Section        8  dac.o(.data)
    .data                                    0x20000034   Section        4  photoswitch.o(.data)
    .data                                    0x20000038   Section       28  usmart.o(.data)
    .data                                    0x20000054   Section      136  usmart_config.o(.data)
    .data                                    0x200000dc   Section      244  usmart_config.o(.data)
    .data                                    0x200001d0   Section       36  gesture.o(.data)
    gesture_names                            0x200001d0   Data          36  gesture.o(.data)
    .data                                    0x200001f4   Section       24  state_machine.o(.data)
    state_names                              0x200001f4   Data          24  state_machine.o(.data)
    .bss                                     0x2000020c   Section       50  main.o(.bss)
    .bss                                     0x20000240   Section      264  usart.o(.bss)
    .bss                                     0x20000348   Section       94  lcd.o(.bss)
    .bss                                     0x200003a8   Section       20  dac.o(.bss)
    .bss                                     0x200003bc   Section       76  fan.o(.bss)
    .bss                                     0x20000408   Section       28  photoswitch.o(.bss)
    .bss                                     0x20000424   Section       60  usmart.o(.bss)
    .bss                                     0x20000460   Section       16  gesture.o(.bss)
    .bss                                     0x20000470   Section       16  state_machine.o(.bss)
    .bss                                     0x20000480   Section       96  libspace.o(.bss)
    HEAP                                     0x200004e0   Section      512  startup_stm32f407xx.o(HEAP)
    Heap_Mem                                 0x200004e0   Data         512  startup_stm32f407xx.o(HEAP)
    STACK                                    0x200006e0   Section     1024  startup_stm32f407xx.o(STACK)
    Stack_Mem                                0x200006e0   Data        1024  startup_stm32f407xx.o(STACK)
    __initial_sp                             0x20000ae0   Data           0  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEJ$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __decompress                             0x080001c5   Thumb Code    90  __dczerorl2.o(!!dczerorl2)
    __decompress1                            0x080001c5   Thumb Code     0  __dczerorl2.o(!!dczerorl2)
    __scatterload_zeroinit                   0x08000221   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_f                                0x0800023d   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_percent                          0x0800023d   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_d                                0x08000243   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x08000249   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_x                                0x0800024f   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_c                                0x08000255   Thumb Code     0  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    _printf_s                                0x0800025b   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_percent_end                      0x08000261   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x08000265   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_1                     0x0800026b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x0800026b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x0800026b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x0800026b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x0800026b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x08000271   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x08000271   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000271   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x08000271   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x0800027b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x0800027b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x0800027b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x0800027b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x0800027b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x0800027b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x0800027b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x0800027b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x0800027b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x0800027b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x0800027b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x0800027b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x0800027b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x0800027d   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x0800027f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x0800027f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x0800027f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x0800027f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x0800027f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x0800027f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x0800027f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x08000281   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000281   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000281   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x08000287   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x08000287   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x0800028b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x0800028b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000293   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000295   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000295   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000299   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x080002a1   Thumb Code     8  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM2_IRQHandler                          0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM6_DAC_IRQHandler                      0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART4_IRQHandler                         0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART5_IRQHandler                         0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART2_IRQHandler                        0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART3_IRQHandler                        0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART6_IRQHandler                        0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x080002bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    __user_initial_stackheap                 0x080002bd   Thumb Code     0  startup_stm32f407xx.o(.text)
    __use_no_semihosting                     0x080002e1   Thumb Code     2  use_no_semi_2.o(.text)
    __2printf                                0x080002e5   Thumb Code    20  noretval__2printf.o(.text)
    __2sprintf                               0x080002fd   Thumb Code    34  noretval__2sprintf.o(.text)
    _printf_pre_padding                      0x08000325   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x08000351   Thumb Code    34  _printf_pad.o(.text)
    _printf_str                              0x08000373   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x080003c5   Thumb Code   104  _printf_dec.o(.text)
    _printf_int_hex                          0x0800043d   Thumb Code    84  _printf_hex_int.o(.text)
    _printf_longlong_hex                     0x0800043d   Thumb Code     0  _printf_hex_int.o(.text)
    __printf                                 0x08000495   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    __aeabi_memclr4                          0x0800061d   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x0800061d   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x0800061d   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x08000621   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x0800066b   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x0800066d   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x0800066f   Thumb Code     2  heapauxi.o(.text)
    __I$use$semihosting                      0x08000671   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000671   Thumb Code     2  use_no_semi.o(.text)
    _printf_int_common                       0x08000673   Thumb Code   178  _printf_intcommon.o(.text)
    __lib_sel_fp_printf                      0x08000725   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x080008d7   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_char_common                      0x08000b4f   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08000b75   Thumb Code    10  _sputc.o(.text)
    _printf_cs_common                        0x08000b7f   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x08000b93   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x08000ba3   Thumb Code     8  _printf_char.o(.text)
    _printf_char_file                        0x08000bad   Thumb Code    32  _printf_char_file.o(.text)
    __rt_locale                              0x08000bd1   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _ll_udiv10                               0x08000bd9   Thumb Code   138  lludiv10.o(.text)
    _printf_fp_infnan                        0x08000c65   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x08000ce5   Thumb Code   224  bigflt0.o(.text)
    ferror                                   0x08000dc9   Thumb Code     8  ferror.o(.text)
    __user_libspace                          0x08000dd1   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000dd1   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000dd1   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x08000dd9   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08000e23   Thumb Code    18  exit.o(.text)
    strcmp                                   0x08000e35   Thumb Code   128  strcmpv7m.o(.text)
    _btod_d2e                                0x08000eb5   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x08000ef3   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x08000f39   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x08000f99   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x080012d1   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x080013ad   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x080013d7   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x08001401   Thumb Code   580  btod.o(CL$$btod_mult_common)
    BusFault_Handler                         0x08001645   Thumb Code     2  stm32f4xx_it.o(i.BusFault_Handler)
    DAC_Init                                 0x08001649   Thumb Code   124  dac.o(i.DAC_Init)
    DAC_SetValue                             0x080016d9   Thumb Code    22  dac.o(i.DAC_SetValue)
    DAC_SetVoltage                           0x080016f5   Thumb Code    58  dac.o(i.DAC_SetVoltage)
    DebugMon_Handler                         0x08001741   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    Display_Init                             0x08001745   Thumb Code   232  main.o(i.Display_Init)
    Display_Update                           0x08001909   Thumb Code   732  main.o(i.Display_Update)
    FSMC_NORSRAM_Extended_Timing_Init        0x08001d71   Thumb Code    60  stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Extended_Timing_Init)
    FSMC_NORSRAM_Init                        0x08001db1   Thumb Code    80  stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Init)
    FSMC_NORSRAM_Timing_Init                 0x08001e05   Thumb Code    64  stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Timing_Init)
    Fan_GetDirection                         0x08001e49   Thumb Code     6  fan.o(i.Fan_GetDirection)
    Fan_GetVoltage                           0x08001e55   Thumb Code     6  fan.o(i.Fan_GetVoltage)
    Fan_Init                                 0x08001e61   Thumb Code   234  fan.o(i.Fan_Init)
    Fan_IsRunning                            0x08001f61   Thumb Code     6  fan.o(i.Fan_IsRunning)
    Fan_SetDirection                         0x08001f6d   Thumb Code    60  fan.o(i.Fan_SetDirection)
    Fan_SetVoltage                           0x08001fb5   Thumb Code    48  fan.o(i.Fan_SetVoltage)
    Fan_Start                                0x08001fe9   Thumb Code    44  fan.o(i.Fan_Start)
    Fan_Stop                                 0x0800201d   Thumb Code    56  fan.o(i.Fan_Stop)
    Fan_Update                               0x08002061   Thumb Code    22  fan.o(i.Fan_Update)
    Gesture_GetName                          0x0800207d   Thumb Code    16  gesture.o(i.Gesture_GetName)
    Gesture_Init                             0x08002099   Thumb Code    20  gesture.o(i.Gesture_Init)
    Gesture_Recognize                        0x080020b1   Thumb Code   208  gesture.o(i.Gesture_Recognize)
    Gesture_Reset                            0x08002185   Thumb Code    18  gesture.o(i.Gesture_Reset)
    HAL_DAC_ConfigChannel                    0x0800219d   Thumb Code    66  stm32f4xx_hal_dac.o(i.HAL_DAC_ConfigChannel)
    HAL_DAC_Init                             0x080021df   Thumb Code    40  stm32f4xx_hal_dac.o(i.HAL_DAC_Init)
    HAL_DAC_MspInit                          0x08002207   Thumb Code     2  stm32f4xx_hal_dac.o(i.HAL_DAC_MspInit)
    HAL_DAC_SetValue                         0x08002209   Thumb Code    26  stm32f4xx_hal_dac.o(i.HAL_DAC_SetValue)
    HAL_DAC_Start                            0x08002223   Thumb Code   108  stm32f4xx_hal_dac.o(i.HAL_DAC_Start)
    HAL_DMA_Abort_IT                         0x0800228f   Thumb Code    36  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_GPIO_Init                            0x080022b5   Thumb Code   402  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x08002475   Thumb Code    10  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GPIO_WritePin                        0x0800247f   Thumb Code    10  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetREVID                             0x08002489   Thumb Code     8  stm32f4xx_hal.o(i.HAL_GetREVID)
    HAL_GetTick                              0x08002495   Thumb Code     6  stm32f4xx_hal.o(i.HAL_GetTick)
    HAL_IncTick                              0x080024a1   Thumb Code    10  stm32f4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x080024b1   Thumb Code    40  stm32f4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x080024dd   Thumb Code    34  stm32f4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08002505   Thumb Code     2  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08002507   Thumb Code    22  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x0800251d   Thumb Code    60  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x0800255d   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08002581   Thumb Code   356  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x080026f5   Thumb Code    34  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08002725   Thumb Code    34  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08002755   Thumb Code   104  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x080027c5   Thumb Code   766  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SRAM_Init                            0x08002ad9   Thumb Code    86  stm32f4xx_hal_sram.o(i.HAL_SRAM_Init)
    HAL_SRAM_MspInit                         0x08002b31   Thumb Code   162  lcd.o(i.HAL_SRAM_MspInit)
    HAL_SYSTICK_CLKSourceConfig              0x08002be9   Thumb Code    24  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig)
    HAL_SYSTICK_Config                       0x08002c01   Thumb Code    40  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIM_Base_Init                        0x08002c29   Thumb Code    54  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08002c5f   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start_IT                    0x08002c61   Thumb Code    24  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    HAL_TIM_PWM_ConfigChannel                0x08002c79   Thumb Code   208  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    HAL_TIM_PWM_Init                         0x08002d49   Thumb Code    54  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    HAL_TIM_PWM_MspInit                      0x08002d7f   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    HAL_TIM_PWM_Start                        0x08002d81   Thumb Code    48  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    HAL_UART_ErrorCallback                   0x08002db9   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_GetState                        0x08002dbb   Thumb Code    10  stm32f4xx_hal_uart.o(i.HAL_UART_GetState)
    HAL_UART_IRQHandler                      0x08002dc5   Thumb Code   266  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x08002ed5   Thumb Code    98  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08002f39   Thumb Code   112  usart.o(i.HAL_UART_MspInit)
    HAL_UART_Receive_IT                      0x08002fb5   Thumb Code    72  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    HAL_UART_RxCpltCallback                  0x08002ffd   Thumb Code    76  usart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_TxCpltCallback                  0x08003055   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x08003057   Thumb Code     2  stm32f4xx_it.o(i.HardFault_Handler)
    LCD_Clear                                0x08003059   Thumb Code    46  lcd.o(i.LCD_Clear)
    LCD_Display_Dir                          0x0800308d   Thumb Code   226  lcd.o(i.LCD_Display_Dir)
    LCD_DrawLine                             0x08003175   Thumb Code   150  lcd.o(i.LCD_DrawLine)
    LCD_DrawPoint                            0x0800320d   Thumb Code    24  lcd.o(i.LCD_DrawPoint)
    LCD_DrawRectangle                        0x08003229   Thumb Code    56  lcd.o(i.LCD_DrawRectangle)
    LCD_Draw_Circle                          0x08003261   Thumb Code   176  lcd.o(i.LCD_Draw_Circle)
    LCD_Fast_DrawPoint                       0x08003311   Thumb Code   202  lcd.o(i.LCD_Fast_DrawPoint)
    LCD_Fill                                 0x080033e1   Thumb Code    70  lcd.o(i.LCD_Fill)
    LCD_Init                                 0x08003429   Thumb Code 11076  lcd.o(i.LCD_Init)
    LCD_Pow                                  0x08005f71   Thumb Code    16  lcd.o(i.LCD_Pow)
    LCD_RD_DATA                              0x08005f81   Thumb Code    20  lcd.o(i.LCD_RD_DATA)
    LCD_ReadPoint                            0x08005f95   Thumb Code   126  lcd.o(i.LCD_ReadPoint)
    LCD_SSD_BackLightSet                     0x08006019   Thumb Code    72  lcd.o(i.LCD_SSD_BackLightSet)
    LCD_Scan_Dir                             0x08006069   Thumb Code   418  lcd.o(i.LCD_Scan_Dir)
    LCD_SetCursor                            0x08006211   Thumb Code   236  lcd.o(i.LCD_SetCursor)
    LCD_ShowChar                             0x08006301   Thumb Code   196  lcd.o(i.LCD_ShowChar)
    LCD_ShowNum                              0x080063dd   Thumb Code   118  lcd.o(i.LCD_ShowNum)
    LCD_ShowString                           0x08006453   Thumb Code    84  lcd.o(i.LCD_ShowString)
    LCD_ShowxNum                             0x080064a7   Thumb Code   134  lcd.o(i.LCD_ShowxNum)
    LCD_WR_DATA                              0x0800652d   Thumb Code    24  lcd.o(i.LCD_WR_DATA)
    LCD_WR_REG                               0x08006545   Thumb Code    24  lcd.o(i.LCD_WR_REG)
    LCD_WriteRAM_Prepare                     0x0800655d   Thumb Code    14  lcd.o(i.LCD_WriteRAM_Prepare)
    LCD_WriteReg                             0x08006571   Thumb Code    12  lcd.o(i.LCD_WriteReg)
    LED_Init                                 0x0800657d   Thumb Code    72  led.o(i.LED_Init)
    MemManage_Handler                        0x080065cd   Thumb Code     2  stm32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x080065cf   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    PendSV_Handler                           0x080065f1   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    PhotoSwitch_GetTriggerTime               0x080065f5   Thumb Code    18  photoswitch.o(i.PhotoSwitch_GetTriggerTime)
    PhotoSwitch_Init                         0x0800660d   Thumb Code   126  photoswitch.o(i.PhotoSwitch_Init)
    PhotoSwitch_IsChanged                    0x0800669d   Thumb Code    16  photoswitch.o(i.PhotoSwitch_IsChanged)
    PhotoSwitch_IsTriggered                  0x080066b1   Thumb Code    18  photoswitch.o(i.PhotoSwitch_IsTriggered)
    PhotoSwitch_Scan                         0x080066c9   Thumb Code   116  photoswitch.o(i.PhotoSwitch_Scan)
    SVC_Handler                              0x08006749   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    StateMachine_GetState                    0x0800674d   Thumb Code     6  state_machine.o(i.StateMachine_GetState)
    StateMachine_GetStateName                0x08006759   Thumb Code    16  state_machine.o(i.StateMachine_GetStateName)
    StateMachine_Init                        0x08006775   Thumb Code    24  state_machine.o(i.StateMachine_Init)
    StateMachine_Process                     0x08006791   Thumb Code   178  state_machine.o(i.StateMachine_Process)
    StateMachine_SetState                    0x080068bd   Thumb Code    60  state_machine.o(i.StateMachine_SetState)
    StateMachine_Update                      0x080068fd   Thumb Code    34  state_machine.o(i.StateMachine_Update)
    Stm32_Clock_Init                         0x08006925   Thumb Code   136  sys.o(i.Stm32_Clock_Init)
    SysTick_Handler                          0x080069b9   Thumb Code     4  stm32f4xx_it.o(i.SysTick_Handler)
    SystemInit                               0x080069bd   Thumb Code    66  system_stm32f4xx.o(i.SystemInit)
    System_Init                              0x08006a11   Thumb Code   116  main.o(i.System_Init)
    TIM4_IRQHandler                          0x08006aa1   Thumb Code    44  usmart.o(i.TIM4_IRQHandler)
    TIM_Base_SetConfig                       0x08006ad5   Thumb Code   148  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_CCxChannelCmd                        0x08006b95   Thumb Code    22  stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    TIM_OC2_SetConfig                        0x08006c05   Thumb Code    90  stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    Timer4_Init                              0x08006d19   Thumb Code    72  usmart.o(i.Timer4_Init)
    USART1_IRQHandler                        0x08007135   Thumb Code    34  usart.o(i.USART1_IRQHandler)
    Ultrasonic_GetDistance                   0x08007161   Thumb Code   102  ultrasonic.o(i.Ultrasonic_GetDistance)
    Ultrasonic_Init                          0x080071d1   Thumb Code    86  ultrasonic.o(i.Ultrasonic_Init)
    Ultrasonic_IsInRange                     0x08007231   Thumb Code    22  ultrasonic.o(i.Ultrasonic_IsInRange)
    Ultrasonic_Trigger                       0x08007249   Thumb Code    34  ultrasonic.o(i.Ultrasonic_Trigger)
    UsageFault_Handler                       0x08007271   Thumb Code     2  stm32f4xx_it.o(i.UsageFault_Handler)
    __ARM_fpclassify                         0x08007273   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    _is_digit                                0x080072a3   Thumb Code    14  __printf_wp.o(i._is_digit)
    _sys_exit                                0x080072b1   Thumb Code     2  usart.o(i._sys_exit)
    delay_init                               0x080072b5   Thumb Code    16  delay.o(i.delay_init)
    delay_ms                                 0x080072c9   Thumb Code    26  delay.o(i.delay_ms)
    delay_us                                 0x080072e5   Thumb Code    48  delay.o(i.delay_us)
    fputc                                    0x08007319   Thumb Code    14  usart.o(i.fputc)
    led_set                                  0x0800732d   Thumb Code     8  main.o(i.led_set)
    main                                     0x08007339   Thumb Code   198  main.o(i.main)
    read_addr                                0x08007415   Thumb Code     4  usmart.o(i.read_addr)
    test_fun                                 0x08007419   Thumb Code   108  main.o(i.test_fun)
    uart_init                                0x080074f1   Thumb Code    44  usart.o(i.uart_init)
    usmart_cmd_rec                           0x08007529   Thumb Code   144  usmart.o(i.usmart_cmd_rec)
    usmart_exe                               0x080075bd   Thumb Code   510  usmart.o(i.usmart_exe)
    usmart_get_aparm                         0x0800781d   Thumb Code   164  usmart_str.o(i.usmart_get_aparm)
    usmart_get_cmdname                       0x080078c1   Thumb Code    46  usmart_str.o(i.usmart_get_cmdname)
    usmart_get_fname                         0x080078f1   Thumb Code   352  usmart_str.o(i.usmart_get_fname)
    usmart_get_fparam                        0x08007a59   Thumb Code   202  usmart_str.o(i.usmart_get_fparam)
    usmart_get_parmpos                       0x08007b29   Thumb Code    30  usmart_str.o(i.usmart_get_parmpos)
    usmart_get_runtime                       0x08007b4d   Thumb Code    38  usmart.o(i.usmart_get_runtime)
    usmart_init                              0x08007b7d   Thumb Code    36  usmart.o(i.usmart_init)
    usmart_pow                               0x08007ba5   Thumb Code    16  usmart_str.o(i.usmart_pow)
    usmart_reset_runtime                     0x08007bb5   Thumb Code    32  usmart.o(i.usmart_reset_runtime)
    usmart_scan                              0x08007bdd   Thumb Code    94  usmart.o(i.usmart_scan)
    usmart_search_nextc                      0x08007c81   Thumb Code    16  usmart_str.o(i.usmart_search_nextc)
    usmart_str2num                           0x08007c91   Thumb Code   214  usmart_str.o(i.usmart_str2num)
    usmart_strcmp                            0x08007d67   Thumb Code    24  usmart_str.o(i.usmart_strcmp)
    usmart_strcopy                           0x08007d7f   Thumb Code    18  usmart_str.o(i.usmart_strcopy)
    usmart_strlen                            0x08007d91   Thumb Code    18  usmart_str.o(i.usmart_strlen)
    usmart_sys_cmd_exe                       0x08007da5   Thumb Code  1458  usmart.o(i.usmart_sys_cmd_exe)
    write_addr                               0x08008425   Thumb Code     4  usmart.o(i.write_addr)
    _get_lc_numeric                          0x08008429   Thumb Code    44  lc_numeric_c.o(locale$$code)
    __aeabi_d2uiz                            0x08008455   Thumb Code     0  dfixu.o(x$fpl$dfixu)
    _dfixu                                   0x08008455   Thumb Code    90  dfixu.o(x$fpl$dfixu)
    __aeabi_ui2d                             0x080084af   Thumb Code     0  dflt_clz.o(x$fpl$dfltu)
    _dfltu                                   0x080084af   Thumb Code    38  dflt_clz.o(x$fpl$dfltu)
    __aeabi_dmul                             0x080084d5   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x080084d5   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x08008629   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x080086c5   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_f2d                              0x080086d1   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x080086d1   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x08008727   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    _fp_init                                 0x080087b3   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x080087bb   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x080087bb   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    _printf_fp_dec                           0x080087bd   Thumb Code     4  printf1.o(x$fpl$printf1)
    AHBPrescTable                            0x080087c0   Data          16  system_stm32f4xx.o(.constdata)
    __I$use$fp                               0x080087c0   Number         0  usenofp.o(x$fpl$usenofp)
    APBPrescTable                            0x080087d0   Data           8  system_stm32f4xx.o(.constdata)
    asc2_1206                                0x080087d8   Data        1140  lcd.o(.constdata)
    asc2_1608                                0x08008c4c   Data        1520  lcd.o(.constdata)
    asc2_2412                                0x0800923c   Data        3420  lcd.o(.constdata)
    asc2_3216                                0x08009f98   Data       12160  lcd.o(.constdata)
    Region$$Table$$Base                      0x0800d374   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800d394   Number         0  anon$$obj.o(Region$$Table)
    current_gesture                          0x20000000   Data           1  main.o(.data)
    system_state                             0x20000001   Data           1  main.o(.data)
    distance                                 0x20000004   Data           4  main.o(.data)
    dac_voltage                              0x20000008   Data           4  main.o(.data)
    last_display_update                      0x2000000c   Data           4  main.o(.data)
    SystemCoreClock                          0x20000010   Data           4  system_stm32f4xx.o(.data)
    uwTick                                   0x20000014   Data           4  stm32f4xx_hal.o(.data)
    aRxBuffer                                0x2000001c   Data           1  usart.o(.data)
    USART_RX_STA                             0x2000001e   Data           2  usart.o(.data)
    __stdout                                 0x20000020   Data           4  usart.o(.data)
    POINT_COLOR                              0x20000024   Data           4  lcd.o(.data)
    BACK_COLOR                               0x20000028   Data           4  lcd.o(.data)
    sConfig                                  0x2000002c   Data           8  dac.o(.data)
    i                                        0x20000034   Data           4  photoswitch.o(.data)
    sys_cmd_tab                              0x20000038   Data          28  usmart.o(.data)
    usmart_nametab                           0x20000054   Data         136  usmart_config.o(.data)
    usmart_dev                               0x200000dc   Data         244  usmart_config.o(.data)
    display_str                              0x2000020c   Data          50  main.o(.bss)
    USART_RX_BUF                             0x20000240   Data         200  usart.o(.bss)
    UART1_Handler                            0x20000308   Data          64  usart.o(.bss)
    TFTSRAM_Handler                          0x20000348   Data          80  lcd.o(.bss)
    lcddev                                   0x20000398   Data          14  lcd.o(.bss)
    hdac                                     0x200003a8   Data          20  dac.o(.bss)
    fan_control                              0x200003bc   Data          16  fan.o(.bss)
    htim_fan                                 0x200003cc   Data          60  fan.o(.bss)
    switch_status                            0x20000408   Data          28  photoswitch.o(.bss)
    TIM4_Handler                             0x20000424   Data          60  usmart.o(.bss)
    gesture_status                           0x20000460   Data          16  gesture.o(.bss)
    state_machine                            0x20000470   Data          16  state_machine.o(.bss)
    __libspace_start                         0x20000480   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200004e0   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000d5bc, Max: 0x00100000, ABSOLUTE, COMPRESSED[0x0000d488])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000d3b0, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO          572    RESET               startup_stm32f407xx.o
    0x08000188   0x08000188   0x00000008   Code   RO         4751  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x00000034   Code   RO         4993    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x080001c4   0x0000005a   Code   RO         4991    !!dczerorl2         c_w.l(__dczerorl2.o)
    0x0800021e   0x0800021e   0x00000002   PAD
    0x08000220   0x08000220   0x0000001c   Code   RO         4995    !!handler_zi        c_w.l(__scatter_zi.o)
    0x0800023c   0x0800023c   0x00000000   Code   RO         4746    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x0800023c   0x0800023c   0x00000006   Code   RO         4745    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x08000242   0x08000242   0x00000006   Code   RO         4743    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000248   0x08000248   0x00000006   Code   RO         4744    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x0800024e   0x0800024e   0x00000006   Code   RO         4742    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x08000254   0x08000254   0x00000006   Code   RO         4740    .ARM.Collect$$_printf_percent$$00000013  c_w.l(_printf_c.o)
    0x0800025a   0x0800025a   0x00000006   Code   RO         4741    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x08000260   0x08000260   0x00000004   Code   RO         4784    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x08000264   0x08000264   0x00000002   Code   RO         4868    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x08000266   0x08000266   0x00000004   Code   RO         4869    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x0800026a   0x0800026a   0x00000000   Code   RO         4872    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x0800026a   0x0800026a   0x00000000   Code   RO         4875    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x0800026a   0x0800026a   0x00000000   Code   RO         4877    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x0800026a   0x0800026a   0x00000000   Code   RO         4879    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x0800026a   0x0800026a   0x00000006   Code   RO         4880    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x08000270   0x08000270   0x00000000   Code   RO         4882    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000270   0x08000270   0x00000000   Code   RO         4884    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000270   0x08000270   0x00000000   Code   RO         4886    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000270   0x08000270   0x0000000a   Code   RO         4887    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x0800027a   0x0800027a   0x00000000   Code   RO         4888    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x0800027a   0x0800027a   0x00000000   Code   RO         4890    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x0800027a   0x0800027a   0x00000000   Code   RO         4892    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x0800027a   0x0800027a   0x00000000   Code   RO         4894    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x0800027a   0x0800027a   0x00000000   Code   RO         4896    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x0800027a   0x0800027a   0x00000000   Code   RO         4898    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x0800027a   0x0800027a   0x00000000   Code   RO         4900    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x0800027a   0x0800027a   0x00000000   Code   RO         4902    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x0800027a   0x0800027a   0x00000000   Code   RO         4906    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x0800027a   0x0800027a   0x00000000   Code   RO         4908    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x0800027a   0x0800027a   0x00000000   Code   RO         4910    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x0800027a   0x0800027a   0x00000000   Code   RO         4912    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x0800027a   0x0800027a   0x00000002   Code   RO         4913    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x0800027c   0x0800027c   0x00000002   Code   RO         4941    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x0800027e   0x0800027e   0x00000000   Code   RO         4950    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x0800027e   0x0800027e   0x00000000   Code   RO         4952    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x0800027e   0x0800027e   0x00000000   Code   RO         4955    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x0800027e   0x0800027e   0x00000000   Code   RO         4958    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x0800027e   0x0800027e   0x00000000   Code   RO         4960    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x0800027e   0x0800027e   0x00000000   Code   RO         4963    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x0800027e   0x0800027e   0x00000002   Code   RO         4964    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x08000280   0x08000280   0x00000000   Code   RO         4771    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000280   0x08000280   0x00000000   Code   RO         4793    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000280   0x08000280   0x00000006   Code   RO         4805    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x08000286   0x08000286   0x00000000   Code   RO         4795    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x08000286   0x08000286   0x00000004   Code   RO         4796    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x0800028a   0x0800028a   0x00000000   Code   RO         4798    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x0800028a   0x0800028a   0x00000008   Code   RO         4799    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x08000292   0x08000292   0x00000002   Code   RO         4914    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000294   0x08000294   0x00000000   Code   RO         4921    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000294   0x08000294   0x00000004   Code   RO         4922    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000298   0x08000298   0x00000006   Code   RO         4923    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x0800029e   0x0800029e   0x00000002   PAD
    0x080002a0   0x080002a0   0x00000040   Code   RO          573    .text               startup_stm32f407xx.o
    0x080002e0   0x080002e0   0x00000002   Code   RO         4681    .text               c_w.l(use_no_semi_2.o)
    0x080002e2   0x080002e2   0x00000002   PAD
    0x080002e4   0x080002e4   0x00000018   Code   RO         4687    .text               c_w.l(noretval__2printf.o)
    0x080002fc   0x080002fc   0x00000028   Code   RO         4689    .text               c_w.l(noretval__2sprintf.o)
    0x08000324   0x08000324   0x0000004e   Code   RO         4693    .text               c_w.l(_printf_pad.o)
    0x08000372   0x08000372   0x00000052   Code   RO         4695    .text               c_w.l(_printf_str.o)
    0x080003c4   0x080003c4   0x00000078   Code   RO         4697    .text               c_w.l(_printf_dec.o)
    0x0800043c   0x0800043c   0x00000058   Code   RO         4702    .text               c_w.l(_printf_hex_int.o)
    0x08000494   0x08000494   0x00000188   Code   RO         4737    .text               c_w.l(__printf_flags_ss_wp.o)
    0x0800061c   0x0800061c   0x0000004e   Code   RO         4747    .text               c_w.l(rt_memclr_w.o)
    0x0800066a   0x0800066a   0x00000006   Code   RO         4749    .text               c_w.l(heapauxi.o)
    0x08000670   0x08000670   0x00000002   Code   RO         4769    .text               c_w.l(use_no_semi.o)
    0x08000672   0x08000672   0x000000b2   Code   RO         4772    .text               c_w.l(_printf_intcommon.o)
    0x08000724   0x08000724   0x0000041e   Code   RO         4774    .text               c_w.l(_printf_fp_dec.o)
    0x08000b42   0x08000b42   0x00000002   PAD
    0x08000b44   0x08000b44   0x00000030   Code   RO         4776    .text               c_w.l(_printf_char_common.o)
    0x08000b74   0x08000b74   0x0000000a   Code   RO         4778    .text               c_w.l(_sputc.o)
    0x08000b7e   0x08000b7e   0x0000002c   Code   RO         4780    .text               c_w.l(_printf_char.o)
    0x08000baa   0x08000baa   0x00000002   PAD
    0x08000bac   0x08000bac   0x00000024   Code   RO         4782    .text               c_w.l(_printf_char_file.o)
    0x08000bd0   0x08000bd0   0x00000008   Code   RO         4810    .text               c_w.l(rt_locale_intlibspace.o)
    0x08000bd8   0x08000bd8   0x0000008a   Code   RO         4812    .text               c_w.l(lludiv10.o)
    0x08000c62   0x08000c62   0x00000002   PAD
    0x08000c64   0x08000c64   0x00000080   Code   RO         4814    .text               c_w.l(_printf_fp_infnan.o)
    0x08000ce4   0x08000ce4   0x000000e4   Code   RO         4818    .text               c_w.l(bigflt0.o)
    0x08000dc8   0x08000dc8   0x00000008   Code   RO         4843    .text               c_w.l(ferror.o)
    0x08000dd0   0x08000dd0   0x00000008   Code   RO         4854    .text               c_w.l(libspace.o)
    0x08000dd8   0x08000dd8   0x0000004a   Code   RO         4857    .text               c_w.l(sys_stackheap_outer.o)
    0x08000e22   0x08000e22   0x00000012   Code   RO         4859    .text               c_w.l(exit.o)
    0x08000e34   0x08000e34   0x00000080   Code   RO         4861    .text               c_w.l(strcmpv7m.o)
    0x08000eb4   0x08000eb4   0x0000003e   Code   RO         4821    CL$$btod_d2e        c_w.l(btod.o)
    0x08000ef2   0x08000ef2   0x00000046   Code   RO         4823    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x08000f38   0x08000f38   0x00000060   Code   RO         4822    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x08000f98   0x08000f98   0x00000338   Code   RO         4831    CL$$btod_div_common  c_w.l(btod.o)
    0x080012d0   0x080012d0   0x000000dc   Code   RO         4828    CL$$btod_e2e        c_w.l(btod.o)
    0x080013ac   0x080013ac   0x0000002a   Code   RO         4825    CL$$btod_ediv       c_w.l(btod.o)
    0x080013d6   0x080013d6   0x0000002a   Code   RO         4824    CL$$btod_emul       c_w.l(btod.o)
    0x08001400   0x08001400   0x00000244   Code   RO         4830    CL$$btod_mult_common  c_w.l(btod.o)
    0x08001644   0x08001644   0x00000002   Code   RO          339    i.BusFault_Handler  stm32f4xx_it.o
    0x08001646   0x08001646   0x00000002   PAD
    0x08001648   0x08001648   0x00000090   Code   RO         4141    i.DAC_Init          dac.o
    0x080016d8   0x080016d8   0x0000001c   Code   RO         4142    i.DAC_SetValue      dac.o
    0x080016f4   0x080016f4   0x0000004c   Code   RO         4143    i.DAC_SetVoltage    dac.o
    0x08001740   0x08001740   0x00000002   Code   RO          340    i.DebugMon_Handler  stm32f4xx_it.o
    0x08001742   0x08001742   0x00000002   PAD
    0x08001744   0x08001744   0x000001c4   Code   RO            9    i.Display_Init      main.o
    0x08001908   0x08001908   0x00000468   Code   RO           10    i.Display_Update    main.o
    0x08001d70   0x08001d70   0x00000040   Code   RO         3321    i.FSMC_NORSRAM_Extended_Timing_Init  stm32f4xx_ll_fsmc.o
    0x08001db0   0x08001db0   0x00000054   Code   RO         3322    i.FSMC_NORSRAM_Init  stm32f4xx_ll_fsmc.o
    0x08001e04   0x08001e04   0x00000044   Code   RO         3323    i.FSMC_NORSRAM_Timing_Init  stm32f4xx_ll_fsmc.o
    0x08001e48   0x08001e48   0x0000000c   Code   RO         4193    i.Fan_GetDirection  fan.o
    0x08001e54   0x08001e54   0x0000000c   Code   RO         4194    i.Fan_GetVoltage    fan.o
    0x08001e60   0x08001e60   0x00000100   Code   RO         4195    i.Fan_Init          fan.o
    0x08001f60   0x08001f60   0x0000000c   Code   RO         4196    i.Fan_IsRunning     fan.o
    0x08001f6c   0x08001f6c   0x00000048   Code   RO         4197    i.Fan_SetDirection  fan.o
    0x08001fb4   0x08001fb4   0x00000034   Code   RO         4198    i.Fan_SetVoltage    fan.o
    0x08001fe8   0x08001fe8   0x00000034   Code   RO         4199    i.Fan_Start         fan.o
    0x0800201c   0x0800201c   0x00000044   Code   RO         4200    i.Fan_Stop          fan.o
    0x08002060   0x08002060   0x0000001c   Code   RO         4201    i.Fan_Update        fan.o
    0x0800207c   0x0800207c   0x0000001c   Code   RO         4532    i.Gesture_GetName   gesture.o
    0x08002098   0x08002098   0x00000018   Code   RO         4533    i.Gesture_Init      gesture.o
    0x080020b0   0x080020b0   0x000000d4   Code   RO         4535    i.Gesture_Recognize  gesture.o
    0x08002184   0x08002184   0x00000018   Code   RO         4536    i.Gesture_Reset     gesture.o
    0x0800219c   0x0800219c   0x00000042   Code   RO         3442    i.HAL_DAC_ConfigChannel  stm32f4xx_hal_dac.o
    0x080021de   0x080021de   0x00000028   Code   RO         3452    i.HAL_DAC_Init      stm32f4xx_hal_dac.o
    0x08002206   0x08002206   0x00000002   Code   RO         3454    i.HAL_DAC_MspInit   stm32f4xx_hal_dac.o
    0x08002208   0x08002208   0x0000001a   Code   RO         3455    i.HAL_DAC_SetValue  stm32f4xx_hal_dac.o
    0x08002222   0x08002222   0x0000006c   Code   RO         3456    i.HAL_DAC_Start     stm32f4xx_hal_dac.o
    0x0800228e   0x0800228e   0x00000024   Code   RO         1843    i.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x080022b2   0x080022b2   0x00000002   PAD
    0x080022b4   0x080022b4   0x000001c0   Code   RO          871    i.HAL_GPIO_Init     stm32f4xx_hal_gpio.o
    0x08002474   0x08002474   0x0000000a   Code   RO          873    i.HAL_GPIO_ReadPin  stm32f4xx_hal_gpio.o
    0x0800247e   0x0800247e   0x0000000a   Code   RO          875    i.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x08002488   0x08002488   0x0000000c   Code   RO          592    i.HAL_GetREVID      stm32f4xx_hal.o
    0x08002494   0x08002494   0x0000000c   Code   RO          593    i.HAL_GetTick       stm32f4xx_hal.o
    0x080024a0   0x080024a0   0x00000010   Code   RO          595    i.HAL_IncTick       stm32f4xx_hal.o
    0x080024b0   0x080024b0   0x0000002c   Code   RO          596    i.HAL_Init          stm32f4xx_hal.o
    0x080024dc   0x080024dc   0x00000028   Code   RO          597    i.HAL_InitTick      stm32f4xx_hal.o
    0x08002504   0x08002504   0x00000002   Code   RO          544    i.HAL_MspInit       stm32f4xx_hal_msp.o
    0x08002506   0x08002506   0x00000016   Code   RO          738    i.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x0800251c   0x0800251c   0x00000040   Code   RO          744    i.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x0800255c   0x0800255c   0x00000024   Code   RO          745    i.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08002580   0x08002580   0x00000174   Code   RO         1109    i.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x080026f4   0x080026f4   0x00000030   Code   RO         1116    i.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x08002724   0x08002724   0x00000030   Code   RO         1117    i.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x08002754   0x08002754   0x00000070   Code   RO         1118    i.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x080027c4   0x080027c4   0x00000314   Code   RO         1121    i.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x08002ad8   0x08002ad8   0x00000056   Code   RO         3197    i.HAL_SRAM_Init     stm32f4xx_hal_sram.o
    0x08002b2e   0x08002b2e   0x00000002   PAD
    0x08002b30   0x08002b30   0x000000b8   Code   RO         3820    i.HAL_SRAM_MspInit  lcd.o
    0x08002be8   0x08002be8   0x00000018   Code   RO          747    i.HAL_SYSTICK_CLKSourceConfig  stm32f4xx_hal_cortex.o
    0x08002c00   0x08002c00   0x00000028   Code   RO          749    i.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x08002c28   0x08002c28   0x00000036   Code   RO         1988    i.HAL_TIM_Base_Init  stm32f4xx_hal_tim.o
    0x08002c5e   0x08002c5e   0x00000002   Code   RO         1990    i.HAL_TIM_Base_MspInit  stm32f4xx_hal_tim.o
    0x08002c60   0x08002c60   0x00000018   Code   RO         1993    i.HAL_TIM_Base_Start_IT  stm32f4xx_hal_tim.o
    0x08002c78   0x08002c78   0x000000d0   Code   RO         2054    i.HAL_TIM_PWM_ConfigChannel  stm32f4xx_hal_tim.o
    0x08002d48   0x08002d48   0x00000036   Code   RO         2057    i.HAL_TIM_PWM_Init  stm32f4xx_hal_tim.o
    0x08002d7e   0x08002d7e   0x00000002   Code   RO         2059    i.HAL_TIM_PWM_MspInit  stm32f4xx_hal_tim.o
    0x08002d80   0x08002d80   0x00000038   Code   RO         2061    i.HAL_TIM_PWM_Start  stm32f4xx_hal_tim.o
    0x08002db8   0x08002db8   0x00000002   Code   RO         1273    i.HAL_UART_ErrorCallback  stm32f4xx_hal_uart.o
    0x08002dba   0x08002dba   0x0000000a   Code   RO         1275    i.HAL_UART_GetState  stm32f4xx_hal_uart.o
    0x08002dc4   0x08002dc4   0x00000110   Code   RO         1276    i.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x08002ed4   0x08002ed4   0x00000062   Code   RO         1277    i.HAL_UART_Init     stm32f4xx_hal_uart.o
    0x08002f36   0x08002f36   0x00000002   PAD
    0x08002f38   0x08002f38   0x0000007c   Code   RO         3730    i.HAL_UART_MspInit  usart.o
    0x08002fb4   0x08002fb4   0x00000048   Code   RO         1282    i.HAL_UART_Receive_IT  stm32f4xx_hal_uart.o
    0x08002ffc   0x08002ffc   0x00000058   Code   RO         3731    i.HAL_UART_RxCpltCallback  usart.o
    0x08003054   0x08003054   0x00000002   Code   RO         1288    i.HAL_UART_TxCpltCallback  stm32f4xx_hal_uart.o
    0x08003056   0x08003056   0x00000002   Code   RO          341    i.HardFault_Handler  stm32f4xx_it.o
    0x08003058   0x08003058   0x00000034   Code   RO         3822    i.LCD_Clear         lcd.o
    0x0800308c   0x0800308c   0x000000e8   Code   RO         3826    i.LCD_Display_Dir   lcd.o
    0x08003174   0x08003174   0x00000096   Code   RO         3827    i.LCD_DrawLine      lcd.o
    0x0800320a   0x0800320a   0x00000002   PAD
    0x0800320c   0x0800320c   0x0000001c   Code   RO         3828    i.LCD_DrawPoint     lcd.o
    0x08003228   0x08003228   0x00000038   Code   RO         3829    i.LCD_DrawRectangle  lcd.o
    0x08003260   0x08003260   0x000000b0   Code   RO         3830    i.LCD_Draw_Circle   lcd.o
    0x08003310   0x08003310   0x000000d0   Code   RO         3831    i.LCD_Fast_DrawPoint  lcd.o
    0x080033e0   0x080033e0   0x00000046   Code   RO         3832    i.LCD_Fill          lcd.o
    0x08003426   0x08003426   0x00000002   PAD
    0x08003428   0x08003428   0x00002b48   Code   RO         3833    i.LCD_Init          lcd.o
    0x08005f70   0x08005f70   0x00000010   Code   RO         3834    i.LCD_Pow           lcd.o
    0x08005f80   0x08005f80   0x00000014   Code   RO         3835    i.LCD_RD_DATA       lcd.o
    0x08005f94   0x08005f94   0x00000084   Code   RO         3836    i.LCD_ReadPoint     lcd.o
    0x08006018   0x08006018   0x00000050   Code   RO         3838    i.LCD_SSD_BackLightSet  lcd.o
    0x08006068   0x08006068   0x000001a8   Code   RO         3839    i.LCD_Scan_Dir      lcd.o
    0x08006210   0x08006210   0x000000f0   Code   RO         3840    i.LCD_SetCursor     lcd.o
    0x08006300   0x08006300   0x000000dc   Code   RO         3842    i.LCD_ShowChar      lcd.o
    0x080063dc   0x080063dc   0x00000076   Code   RO         3843    i.LCD_ShowNum       lcd.o
    0x08006452   0x08006452   0x00000054   Code   RO         3844    i.LCD_ShowString    lcd.o
    0x080064a6   0x080064a6   0x00000086   Code   RO         3845    i.LCD_ShowxNum      lcd.o
    0x0800652c   0x0800652c   0x00000018   Code   RO         3846    i.LCD_WR_DATA       lcd.o
    0x08006544   0x08006544   0x00000018   Code   RO         3847    i.LCD_WR_REG        lcd.o
    0x0800655c   0x0800655c   0x00000014   Code   RO         3849    i.LCD_WriteRAM_Prepare  lcd.o
    0x08006570   0x08006570   0x0000000c   Code   RO         3850    i.LCD_WriteReg      lcd.o
    0x0800657c   0x0800657c   0x00000050   Code   RO         3796    i.LED_Init          led.o
    0x080065cc   0x080065cc   0x00000002   Code   RO          342    i.MemManage_Handler  stm32f4xx_it.o
    0x080065ce   0x080065ce   0x00000002   Code   RO          343    i.NMI_Handler       stm32f4xx_it.o
    0x080065d0   0x080065d0   0x00000020   Code   RO          751    i.NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x080065f0   0x080065f0   0x00000002   Code   RO          344    i.PendSV_Handler    stm32f4xx_it.o
    0x080065f2   0x080065f2   0x00000002   PAD
    0x080065f4   0x080065f4   0x00000018   Code   RO         4272    i.PhotoSwitch_GetTriggerTime  photoswitch.o
    0x0800660c   0x0800660c   0x00000090   Code   RO         4273    i.PhotoSwitch_Init  photoswitch.o
    0x0800669c   0x0800669c   0x00000014   Code   RO         4274    i.PhotoSwitch_IsChanged  photoswitch.o
    0x080066b0   0x080066b0   0x00000018   Code   RO         4276    i.PhotoSwitch_IsTriggered  photoswitch.o
    0x080066c8   0x080066c8   0x00000080   Code   RO         4277    i.PhotoSwitch_Scan  photoswitch.o
    0x08006748   0x08006748   0x00000002   Code   RO          345    i.SVC_Handler       stm32f4xx_it.o
    0x0800674a   0x0800674a   0x00000002   PAD
    0x0800674c   0x0800674c   0x0000000c   Code   RO         4593    i.StateMachine_GetState  state_machine.o
    0x08006758   0x08006758   0x0000001c   Code   RO         4594    i.StateMachine_GetStateName  state_machine.o
    0x08006774   0x08006774   0x0000001c   Code   RO         4595    i.StateMachine_Init  state_machine.o
    0x08006790   0x08006790   0x000000c0   Code   RO         4597    i.StateMachine_Process  state_machine.o
    0x08006850   0x08006850   0x0000006c   Code   RO         4598    i.StateMachine_ProcessFanControl  state_machine.o
    0x080068bc   0x080068bc   0x00000040   Code   RO         4599    i.StateMachine_SetState  state_machine.o
    0x080068fc   0x080068fc   0x00000028   Code   RO         4600    i.StateMachine_Update  state_machine.o
    0x08006924   0x08006924   0x00000094   Code   RO         3703    i.Stm32_Clock_Init  sys.o
    0x080069b8   0x080069b8   0x00000004   Code   RO          346    i.SysTick_Handler   stm32f4xx_it.o
    0x080069bc   0x080069bc   0x00000054   Code   RO          418    i.SystemInit        system_stm32f4xx.o
    0x08006a10   0x08006a10   0x00000090   Code   RO           11    i.System_Init       main.o
    0x08006aa0   0x08006aa0   0x00000034   Code   RO         4338    i.TIM4_IRQHandler   usmart.o
    0x08006ad4   0x08006ad4   0x000000c0   Code   RO         2072    i.TIM_Base_SetConfig  stm32f4xx_hal_tim.o
    0x08006b94   0x08006b94   0x00000016   Code   RO         2073    i.TIM_CCxChannelCmd  stm32f4xx_hal_tim.o
    0x08006baa   0x08006baa   0x00000002   PAD
    0x08006bac   0x08006bac   0x00000058   Code   RO         2081    i.TIM_OC1_SetConfig  stm32f4xx_hal_tim.o
    0x08006c04   0x08006c04   0x00000064   Code   RO         2082    i.TIM_OC2_SetConfig  stm32f4xx_hal_tim.o
    0x08006c68   0x08006c68   0x00000060   Code   RO         2083    i.TIM_OC3_SetConfig  stm32f4xx_hal_tim.o
    0x08006cc8   0x08006cc8   0x00000050   Code   RO         2084    i.TIM_OC4_SetConfig  stm32f4xx_hal_tim.o
    0x08006d18   0x08006d18   0x00000054   Code   RO         4339    i.Timer4_Init       usmart.o
    0x08006d6c   0x08006d6c   0x00000010   Code   RO         1290    i.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x08006d7c   0x08006d7c   0x0000001c   Code   RO         1300    i.UART_EndRxTransfer  stm32f4xx_hal_uart.o
    0x08006d98   0x08006d98   0x0000008c   Code   RO         1302    i.UART_Receive_IT   stm32f4xx_hal_uart.o
    0x08006e24   0x08006e24   0x000002ac   Code   RO         1303    i.UART_SetConfig    stm32f4xx_hal_uart.o
    0x080070d0   0x080070d0   0x00000062   Code   RO         1304    i.UART_Transmit_IT  stm32f4xx_hal_uart.o
    0x08007132   0x08007132   0x00000002   PAD
    0x08007134   0x08007134   0x0000002c   Code   RO         3732    i.USART1_IRQHandler  usart.o
    0x08007160   0x08007160   0x00000070   Code   RO         4094    i.Ultrasonic_GetDistance  ultrasonic.o
    0x080071d0   0x080071d0   0x00000060   Code   RO         4095    i.Ultrasonic_Init   ultrasonic.o
    0x08007230   0x08007230   0x00000016   Code   RO         4096    i.Ultrasonic_IsInRange  ultrasonic.o
    0x08007246   0x08007246   0x00000002   PAD
    0x08007248   0x08007248   0x00000028   Code   RO         4097    i.Ultrasonic_Trigger  ultrasonic.o
    0x08007270   0x08007270   0x00000002   Code   RO          347    i.UsageFault_Handler  stm32f4xx_it.o
    0x08007272   0x08007272   0x00000030   Code   RO         4852    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x080072a2   0x080072a2   0x0000000e   Code   RO         4730    i._is_digit         c_w.l(__printf_wp.o)
    0x080072b0   0x080072b0   0x00000002   Code   RO         3733    i._sys_exit         usart.o
    0x080072b2   0x080072b2   0x00000002   PAD
    0x080072b4   0x080072b4   0x00000014   Code   RO         3664    i.delay_init        delay.o
    0x080072c8   0x080072c8   0x0000001a   Code   RO         3665    i.delay_ms          delay.o
    0x080072e2   0x080072e2   0x00000002   PAD
    0x080072e4   0x080072e4   0x00000034   Code   RO         3666    i.delay_us          delay.o
    0x08007318   0x08007318   0x00000014   Code   RO         3734    i.fputc             usart.o
    0x0800732c   0x0800732c   0x0000000c   Code   RO           12    i.led_set           main.o
    0x08007338   0x08007338   0x000000dc   Code   RO           13    i.main              main.o
    0x08007414   0x08007414   0x00000004   Code   RO         4340    i.read_addr         usmart.o
    0x08007418   0x08007418   0x000000d8   Code   RO           14    i.test_fun          main.o
    0x080074f0   0x080074f0   0x00000038   Code   RO         3735    i.uart_init         usart.o
    0x08007528   0x08007528   0x00000094   Code   RO         4341    i.usmart_cmd_rec    usmart.o
    0x080075bc   0x080075bc   0x00000260   Code   RO         4342    i.usmart_exe        usmart.o
    0x0800781c   0x0800781c   0x000000a4   Code   RO         4427    i.usmart_get_aparm  usmart_str.o
    0x080078c0   0x080078c0   0x0000002e   Code   RO         4428    i.usmart_get_cmdname  usmart_str.o
    0x080078ee   0x080078ee   0x00000002   PAD
    0x080078f0   0x080078f0   0x00000168   Code   RO         4429    i.usmart_get_fname  usmart_str.o
    0x08007a58   0x08007a58   0x000000d0   Code   RO         4430    i.usmart_get_fparam  usmart_str.o
    0x08007b28   0x08007b28   0x00000024   Code   RO         4431    i.usmart_get_parmpos  usmart_str.o
    0x08007b4c   0x08007b4c   0x00000030   Code   RO         4343    i.usmart_get_runtime  usmart.o
    0x08007b7c   0x08007b7c   0x00000028   Code   RO         4344    i.usmart_init       usmart.o
    0x08007ba4   0x08007ba4   0x00000010   Code   RO         4432    i.usmart_pow        usmart_str.o
    0x08007bb4   0x08007bb4   0x00000028   Code   RO         4345    i.usmart_reset_runtime  usmart.o
    0x08007bdc   0x08007bdc   0x000000a4   Code   RO         4346    i.usmart_scan       usmart.o
    0x08007c80   0x08007c80   0x00000010   Code   RO         4433    i.usmart_search_nextc  usmart_str.o
    0x08007c90   0x08007c90   0x000000d6   Code   RO         4434    i.usmart_str2num    usmart_str.o
    0x08007d66   0x08007d66   0x00000018   Code   RO         4435    i.usmart_strcmp     usmart_str.o
    0x08007d7e   0x08007d7e   0x00000012   Code   RO         4436    i.usmart_strcopy    usmart_str.o
    0x08007d90   0x08007d90   0x00000012   Code   RO         4437    i.usmart_strlen     usmart_str.o
    0x08007da2   0x08007da2   0x00000002   PAD
    0x08007da4   0x08007da4   0x00000680   Code   RO         4347    i.usmart_sys_cmd_exe  usmart.o
    0x08008424   0x08008424   0x00000004   Code   RO         4348    i.write_addr        usmart.o
    0x08008428   0x08008428   0x0000002c   Code   RO         4848    locale$$code        c_w.l(lc_numeric_c.o)
    0x08008454   0x08008454   0x0000005a   Code   RO         4753    x$fpl$dfixu         fz_wm.l(dfixu.o)
    0x080084ae   0x080084ae   0x00000026   Code   RO         4757    x$fpl$dfltu         fz_wm.l(dflt_clz.o)
    0x080084d4   0x080084d4   0x00000154   Code   RO         4763    x$fpl$dmul          fz_wm.l(dmul.o)
    0x08008628   0x08008628   0x0000009c   Code   RO         4785    x$fpl$dnaninf       fz_wm.l(dnaninf.o)
    0x080086c4   0x080086c4   0x0000000c   Code   RO         4787    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x080086d0   0x080086d0   0x00000056   Code   RO         4765    x$fpl$f2d           fz_wm.l(f2d.o)
    0x08008726   0x08008726   0x0000008c   Code   RO         4789    x$fpl$fnaninf       fz_wm.l(fnaninf.o)
    0x080087b2   0x080087b2   0x0000000a   Code   RO         4918    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x080087bc   0x080087bc   0x00000004   Code   RO         4767    x$fpl$printf1       fz_wm.l(printf1.o)
    0x080087c0   0x080087c0   0x00000000   Code   RO         4791    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x080087c0   0x080087c0   0x00000010   Data   RO          419    .constdata          system_stm32f4xx.o
    0x080087d0   0x080087d0   0x00000008   Data   RO          420    .constdata          system_stm32f4xx.o
    0x080087d8   0x080087d8   0x00004740   Data   RO         3853    .constdata          lcd.o
    0x0800cf18   0x0800cf18   0x00000028   Data   RO         4703    .constdata          c_w.l(_printf_hex_int.o)
    0x0800cf40   0x0800cf40   0x00000011   Data   RO         4738    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x0800cf51   0x0800cf51   0x00000003   PAD
    0x0800cf54   0x0800cf54   0x00000094   Data   RO         4819    .constdata          c_w.l(bigflt0.o)
    0x0800cfe8   0x0800cfe8   0x00000028   Data   RO         4350    .conststring        usmart.o
    0x0800d010   0x0800d010   0x000002c7   Data   RO         4506    .conststring        usmart_config.o
    0x0800d2d7   0x0800d2d7   0x00000001   PAD
    0x0800d2d8   0x0800d2d8   0x00000045   Data   RO         4538    .conststring        gesture.o
    0x0800d31d   0x0800d31d   0x00000003   PAD
    0x0800d320   0x0800d320   0x00000054   Data   RO         4602    .conststring        state_machine.o
    0x0800d374   0x0800d374   0x00000020   Data   RO         4989    Region$$Table       anon$$obj.o
    0x0800d394   0x0800d394   0x0000001c   Data   RO         4847    locale$$data        c_w.l(lc_numeric_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0800d3b0, Size: 0x00000ae0, Max: 0x00020000, ABSOLUTE, COMPRESSED[0x000000d8])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x00000010   Data   RW           16    .data               main.o
    0x20000010   COMPRESSED   0x00000004   Data   RW          421    .data               system_stm32f4xx.o
    0x20000014   COMPRESSED   0x00000004   Data   RW          602    .data               stm32f4xx_hal.o
    0x20000018   COMPRESSED   0x00000004   Data   RW         3667    .data               delay.o
    0x2000001c   COMPRESSED   0x00000004   Data   RW         3737    .data               usart.o
    0x20000020   COMPRESSED   0x00000004   Data   RW         3738    .data               usart.o
    0x20000024   COMPRESSED   0x00000008   Data   RW         3854    .data               lcd.o
    0x2000002c   COMPRESSED   0x00000008   Data   RW         4145    .data               dac.o
    0x20000034   COMPRESSED   0x00000004   Data   RW         4279    .data               photoswitch.o
    0x20000038   COMPRESSED   0x0000001c   Data   RW         4351    .data               usmart.o
    0x20000054   COMPRESSED   0x00000088   Data   RW         4507    .data               usmart_config.o
    0x200000dc   COMPRESSED   0x000000f4   Data   RW         4508    .data               usmart_config.o
    0x200001d0   COMPRESSED   0x00000024   Data   RW         4539    .data               gesture.o
    0x200001f4   COMPRESSED   0x00000018   Data   RW         4603    .data               state_machine.o
    0x2000020c        -       0x00000032   Zero   RW           15    .bss                main.o
    0x2000023e   COMPRESSED   0x00000002   PAD
    0x20000240        -       0x00000108   Zero   RW         3736    .bss                usart.o
    0x20000348        -       0x0000005e   Zero   RW         3852    .bss                lcd.o
    0x200003a6   COMPRESSED   0x00000002   PAD
    0x200003a8        -       0x00000014   Zero   RW         4144    .bss                dac.o
    0x200003bc        -       0x0000004c   Zero   RW         4202    .bss                fan.o
    0x20000408        -       0x0000001c   Zero   RW         4278    .bss                photoswitch.o
    0x20000424        -       0x0000003c   Zero   RW         4349    .bss                usmart.o
    0x20000460        -       0x00000010   Zero   RW         4537    .bss                gesture.o
    0x20000470        -       0x00000010   Zero   RW         4601    .bss                state_machine.o
    0x20000480        -       0x00000060   Zero   RW         4855    .bss                c_w.l(libspace.o)
    0x200004e0        -       0x00000200   Zero   RW          571    HEAP                startup_stm32f407xx.o
    0x200006e0        -       0x00000400   Zero   RW          570    STACK               startup_stm32f407xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       248         44          0          8         20       2443   dac.o
        98          8          0          4          0       2039   delay.o
       564         82          0          0         76       6534   fan.o
       288         26         69         36         16       5330   gesture.o
     13784        152      18240          8         94      27687   lcd.o
        80          8          0          0          0        915   led.o
      2172        788          0         16         50     701457   main.o
       340         46          0          4         28       5071   photoswitch.o
        64         26        392          0       1536        852   startup_stm32f407xx.o
       472         64         84         24         16       8082   state_machine.o
       124         26          0          4          0       4526   stm32f4xx_hal.o
       218         14          0          0          0      31053   stm32f4xx_hal_cortex.o
       242          0          0          0          0       4268   stm32f4xx_hal_dac.o
        36          0          0          0          0       1263   stm32f4xx_hal_dma.o
       468         46          0          0          0       3299   stm32f4xx_hal_gpio.o
         2          0          0          0          0        466   stm32f4xx_hal_msp.o
      1368         74          0          0          0       5464   stm32f4xx_hal_rcc.o
        86          0          0          0          0       1260   stm32f4xx_hal_sram.o
       978         88          0          0          0      11041   stm32f4xx_hal_tim.o
      1422         14          0          0          0       8655   stm32f4xx_hal_uart.o
        20          0          0          0          0       4190   stm32f4xx_it.o
       216         12          0          0          0       2799   stm32f4xx_ll_fsmc.o
       148         12          0          0          0       1191   sys.o
        84         18         24          4          0       1159   system_stm32f4xx.o
       270         26          0          0          0       3075   ultrasonic.o
       334         52          0          8        264       5573   usart.o
      2856       1354         40         28         60       8275   usmart.o
         0          0        711        380          0       1596   usmart_config.o
      1120         20          0          0          0       9807   usmart_str.o

    ----------------------------------------------------------------------
     28134       <USER>      <GROUP>        524       2164     869370   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        32          0          4          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        90          0          0          0          0          0   __dczerorl2.o
         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        28          0          0          0          0          0   __scatter_zi.o
         6          0          0          0          0          0   _printf_c.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        36          4          0          0          0         80   _printf_char_file.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       128         16          0          0          0         84   _printf_fp_infnan.o
        88          4         40          0          0         88   _printf_hex_int.o
       178          0          0          0          0         88   _printf_intcommon.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
         6          0          0          0          0          0   _printf_u.o
         6          0          0          0          0          0   _printf_x.o
        10          0          0          0          0         68   _sputc.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        18          0          0          0          0         80   exit.o
         8          0          0          0          0         68   ferror.o
         6          0          0          0          0        152   heapauxi.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        22          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
        24          4          0          0          0         84   noretval__2printf.o
        40          6          0          0          0         84   noretval__2sprintf.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        78          0          0          0          0         80   rt_memclr_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       128          0          0          0          0         68   strcmpv7m.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
        90          4          0          0          0        140   dfixu.o
        38          0          0          0          0        116   dflt_clz.o
       340         12          0          0          0        152   dmul.o
       156          4          0          0          0        140   dnaninf.o
        12          0          0          0          0        116   dretinf.o
        86          4          0          0          0        132   f2d.o
       140          4          0          0          0        132   fnaninf.o
        10          0          0          0          0        116   fpinit.o
         4          0          0          0          0        116   printf1.o
         0          0          0          0          0          0   usenofp.o
        48          0          0          0          0        124   fpclassify.o

    ----------------------------------------------------------------------
      6226        <USER>        <GROUP>          0         96       4512   Library Totals
        12          0          3          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      5290        218        233          0         96       3228   c_w.l
       876         28          0          0          0       1160   fz_wm.l
        48          0          0          0          0        124   m_wm.l

    ----------------------------------------------------------------------
      6226        <USER>        <GROUP>          0         96       4512   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     34360       3246      19832        524       2260     856314   Grand Totals
     34360       3246      19832        216       2260     856314   ELF Image Totals (compressed)
     34360       3246      19832        216          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                54192 (  52.92kB)
    Total RW  Size (RW Data + ZI Data)              2784 (   2.72kB)
    Total ROM Size (Code + RO Data + RW Data)      54408 (  53.13kB)

==============================================================================

