#ifndef __ULTRASONIC_H
#define __ULTRASONIC_H
#include "sys.h"

//////////////////////////////////////////////////////////////////////////////////	 
// STM32F407 超声波测距模块
// 支持HC-SR04超声波传感器
// 引脚定义：TRIG -> PD3, ECHO -> PD6
//////////////////////////////////////////////////////////////////////////////////

// 超声波测距相关定义
#define TRIG_PORT GPIOD
#define TRIG_PIN  GPIO_PIN_3
#define ECHO_PORT GPIOD
#define ECHO_PIN  GPIO_PIN_6

#define TRIG_HIGH HAL_GPIO_WritePin(TRIG_PORT, TRIG_PIN, GPIO_PIN_SET)
#define TRIG_LOW  HAL_GPIO_WritePin(TRIG_PORT, TRIG_PIN, GPIO_PIN_RESET)
#define ECHO_READ HAL_GPIO_ReadPin(ECHO_PORT, ECHO_PIN)

// 测距参数
#define ULTRASONIC_MAX_DISTANCE  400.0f  // 最大测距范围(cm)
#define ULTRASONIC_MIN_DISTANCE  2.0f    // 最小测距范围(cm)
#define ULTRASONIC_TIMEOUT       30000   // 超时时间(us)

// 函数声明
void Ultrasonic_Init(void);                    // 超声波模块初始化
void Ultrasonic_Trigger(void);                 // 发送触发脉冲
float Ultrasonic_GetDistance(void);            // 获取距离测量值
uint8_t Ultrasonic_IsInRange(float distance);  // 判断距离是否在有效范围内

#endif
