#ifndef __STATE_MACHINE_H
#define __STATE_MACHINE_H
#include "sys.h"
#include "../APP/GESTURE/gesture.h"
#include "../HARDWARE/FAN/fan.h"

//////////////////////////////////////////////////////////////////////////////////
// 非接触式控制盘 - 状态机模块
//
// 【功能说明】
// 管理系统各种工作状态和状态转换，实现复杂的控制逻辑
// 采用有限状态机(FSM)设计模式，确保系统行为的可预测性
//
// 【状态机设计】
// IDLE: 待机状态 - 系统初始状态，等待用户手势输入
// FAN_CONTROL: 风扇控制状态 - 处理风扇启停、调速等基本操作
// TIME_SETTING: 时间设定状态 - 根据距离设定风扇运行时间(15-30s)
// VOLTAGE_SETTING: 电压设定状态 - 根据距离设定风扇工作电压(3-10.5V)
// COMBO_SETTING: 组合操作设定状态 - 录制复杂的操作序列
// COMBO_RUNNING: 组合操作运行状态 - 执行预设的操作序列
//
// 【状态转换条件】
// - 手势触发：不同手势在不同状态下产生不同的转换
// - 距离条件：某些功能需要特定的距离范围(5-20cm)
// - 超时机制：设定状态10s超时，组合操作60s超时
// - 异常处理：任何异常情况都会返回IDLE状态
//////////////////////////////////////////////////////////////////////////////////

// 系统状态枚举
typedef enum {
    STATE_IDLE = 0,         // 待机状态
    STATE_FAN_CONTROL,      // 风扇控制状态
    STATE_TIME_SETTING,     // 时间设定状态
    STATE_VOLTAGE_SETTING,  // 电压设定状态
    STATE_COMBO_SETTING,    // 组合操作设定状态
    STATE_COMBO_RUNNING,    // 组合操作运行状态
    STATE_COUNT
} SystemState_t;

// 风扇控制子状态
typedef enum {
    FAN_STATE_STOPPED = 0,  // 停止
    FAN_STATE_FORWARD,      // 正转
    FAN_STATE_REVERSE,      // 反转
    FAN_STATE_COUNT
} FanState_t;

// 状态机结构体
typedef struct {
    SystemState_t current_state;    // 当前状态
    SystemState_t last_state;       // 上次状态
    FanState_t fan_state;           // 风扇状态
    uint32_t state_enter_time;      // 状态进入时间
    uint32_t state_timeout;         // 状态超时时间
    uint8_t state_changed;          // 状态改变标志
} StateMachine_t;

// 状态超时定义
#define STATE_TIMEOUT_SETTING   10000   // 设定状态超时10s
#define STATE_TIMEOUT_COMBO     60000   // 组合操作超时60s

// 全局变量声明
extern StateMachine_t state_machine;

// 函数声明
void StateMachine_Init(void);                           // 状态机初始化
void StateMachine_Process(GestureType_t gesture);       // 状态机处理
void StateMachine_SetState(SystemState_t new_state);    // 设置状态
SystemState_t StateMachine_GetState(void);              // 获取当前状态
FanState_t StateMachine_GetFanState(void);              // 获取风扇状态
uint8_t StateMachine_IsStateChanged(void);              // 检查状态是否改变
void StateMachine_Update(void);                         // 状态机更新
const char* StateMachine_GetStateName(SystemState_t state); // 获取状态名称

#endif
