#include "pwm.h"

//////////////////////////////////////////////////////////////////////////////////	 
// STM32F407 PWM输出模块实现
// 使用TIM3_CH1输出PWM信号
//////////////////////////////////////////////////////////////////////////////////

// 全局变量定义
TIM_HandleTypeDef htim_pwm;
TIM_OC_InitTypeDef sConfigOC;

/**
 * @brief  PWM模块初始化
 * @param  None
 * @retval None
 */
void PWM_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    
    // 使能时钟
    __HAL_RCC_GPIOA_CLK_ENABLE();
    PWM_TIM_CLK_ENABLE();
    
    // 配置GPIO
    GPIO_InitStruct.Pin = PWM_GPIO_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    GPIO_InitStruct.Alternate = PWM_GPIO_AF;
    HAL_GPIO_Init(PWM_GPIO_PORT, &GPIO_InitStruct);
    
    // 配置定时器
    htim_pwm.Instance = PWM_TIM;
    htim_pwm.Init.Prescaler = 84-1;           // 84MHz/84 = 1MHz
    htim_pwm.Init.CounterMode = TIM_COUNTERMODE_UP;
    htim_pwm.Init.Period = PWM_PERIOD-1;      // 1000-1 = 999, 1MHz/1000 = 1kHz
    htim_pwm.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;
    
    if (HAL_TIM_PWM_Init(&htim_pwm) != HAL_OK)
    {
        // 初始化错误处理
        while(1);
    }
    
    // 配置PWM通道
    sConfigOC.OCMode = TIM_OCMODE_PWM1;
    sConfigOC.Pulse = 0;                      // 初始占空比为0
    sConfigOC.OCPolarity = TIM_OCPOLARITY_HIGH;
    sConfigOC.OCFastMode = TIM_OCFAST_DISABLE;
    
    if (HAL_TIM_PWM_ConfigChannel(&htim_pwm, &sConfigOC, TIM_CHANNEL_1) != HAL_OK)
    {
        // 配置错误处理
        while(1);
    }
}

/**
 * @brief  设置PWM占空比
 * @param  duty: 占空比值 (0-1000)
 * @retval None
 */
void PWM_SetDuty(uint16_t duty)
{
    if(duty > PWM_MAX_DUTY) duty = PWM_MAX_DUTY;
    
    __HAL_TIM_SET_COMPARE(&htim_pwm, TIM_CHANNEL_1, duty);
}

/**
 * @brief  设置PWM频率
 * @param  frequency: 频率值 (Hz)
 * @retval None
 */
void PWM_SetFrequency(uint32_t frequency)
{
    uint32_t period;
    
    if(frequency == 0) return;
    
    // 计算新的周期值
    period = 1000000 / frequency;  // 1MHz时钟源
    
    // 停止PWM
    HAL_TIM_PWM_Stop(&htim_pwm, TIM_CHANNEL_1);
    
    // 更新周期
    __HAL_TIM_SET_AUTORELOAD(&htim_pwm, period-1);
    
    // 重新启动PWM
    HAL_TIM_PWM_Start(&htim_pwm, TIM_CHANNEL_1);
}

/**
 * @brief  启动PWM输出
 * @param  None
 * @retval None
 */
void PWM_Start(void)
{
    HAL_TIM_PWM_Start(&htim_pwm, TIM_CHANNEL_1);
}

/**
 * @brief  停止PWM输出
 * @param  None
 * @retval None
 */
void PWM_Stop(void)
{
    HAL_TIM_PWM_Stop(&htim_pwm, TIM_CHANNEL_1);
}

/**
 * @brief  获取当前PWM占空比
 * @param  None
 * @retval 当前占空比值
 */
uint16_t PWM_GetDuty(void)
{
    return __HAL_TIM_GET_COMPARE(&htim_pwm, TIM_CHANNEL_1);
}
