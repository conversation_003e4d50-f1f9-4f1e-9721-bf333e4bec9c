#include "fan.h"

//////////////////////////////////////////////////////////////////////////////////	 
// 非接触式控制盘 - 风扇控制模块实现
//////////////////////////////////////////////////////////////////////////////////

// 全局变量定义
FanControl_t fan_control;
TIM_HandleTypeDef htim_fan;

/**
 * @brief  风扇模块初始化
 * @param  None
 * @retval None
 */
void Fan_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    TIM_OC_InitTypeDef sConfigOC = {0};
    
    // 使能时钟
    __HAL_RCC_GPIOA_CLK_ENABLE();
    __HAL_RCC_GPIOB_CLK_ENABLE();
    __HAL_RCC_TIM3_CLK_ENABLE();
    
    // 配置PWM引脚
    GPIO_InitStruct.Pin = FAN_PWM_GPIO_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF2_TIM3;
    HAL_GPIO_Init(FAN_PWM_GPIO_PORT, &GPIO_InitStruct);
    
    // 配置方向控制引脚
    GPIO_InitStruct.Pin = FAN_DIR1_GPIO_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    GPIO_InitStruct.Alternate = 0;
    HAL_GPIO_Init(FAN_DIR1_GPIO_PORT, &GPIO_InitStruct);
    
    GPIO_InitStruct.Pin = FAN_DIR2_GPIO_PIN;
    HAL_GPIO_Init(FAN_DIR2_GPIO_PORT, &GPIO_InitStruct);
    
    GPIO_InitStruct.Pin = FAN_EN_GPIO_PIN;
    HAL_GPIO_Init(FAN_EN_GPIO_PORT, &GPIO_InitStruct);
    
    // 配置定时器
    htim_fan.Instance = TIM3;
    htim_fan.Init.Prescaler = 84-1;           // 84MHz/84 = 1MHz
    htim_fan.Init.CounterMode = TIM_COUNTERMODE_UP;
    htim_fan.Init.Period = FAN_PWM_PERIOD-1;  // 1MHz/1000 = 1kHz
    htim_fan.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;
    
    if (HAL_TIM_PWM_Init(&htim_fan) != HAL_OK)
    {
        while(1); // 初始化错误
    }
    
    // 配置PWM通道
    sConfigOC.OCMode = TIM_OCMODE_PWM1;
    sConfigOC.Pulse = 0;
    sConfigOC.OCPolarity = TIM_OCPOLARITY_HIGH;
    sConfigOC.OCFastMode = TIM_OCFAST_DISABLE;
    
    if (HAL_TIM_PWM_ConfigChannel(&htim_fan, &sConfigOC, TIM_CHANNEL_1) != HAL_OK)
    {
        while(1); // 配置错误
    }
    
    // 启动PWM
    HAL_TIM_PWM_Start(&htim_fan, TIM_CHANNEL_1);
    
    // 初始化风扇控制结构体
    fan_control.direction = FAN_STOP;
    fan_control.voltage_mv = FAN_VOLTAGE_MIN;
    fan_control.pwm_duty = 0;
    fan_control.is_running = 0;
    fan_control.run_time_ms = 0;
    fan_control.start_time = 0;
    
    // 初始状态：停止
    Fan_Stop();
}

/**
 * @brief  启动风扇
 * @param  direction: 转向
 * @retval None
 */
void Fan_Start(FanDirection_t direction)
{
    fan_control.direction = direction;
    fan_control.is_running = 1;
    fan_control.start_time = HAL_GetTick();
    
    // 设置方向
    Fan_SetDirection(direction);
    
    // 使能输出
    HAL_GPIO_WritePin(FAN_EN_GPIO_PORT, FAN_EN_GPIO_PIN, GPIO_PIN_SET);
    
    // 设置PWM占空比
    Fan_SetVoltage(fan_control.voltage_mv);
}

/**
 * @brief  停止风扇
 * @param  None
 * @retval None
 */
void Fan_Stop(void)
{
    fan_control.direction = FAN_STOP;
    fan_control.is_running = 0;
    fan_control.pwm_duty = 0;
    
    // 禁用输出
    HAL_GPIO_WritePin(FAN_EN_GPIO_PORT, FAN_EN_GPIO_PIN, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(FAN_DIR1_GPIO_PORT, FAN_DIR1_GPIO_PIN, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(FAN_DIR2_GPIO_PORT, FAN_DIR2_GPIO_PIN, GPIO_PIN_RESET);
    
    // 停止PWM
    __HAL_TIM_SET_COMPARE(&htim_fan, TIM_CHANNEL_1, 0);
}

/**
 * @brief  设置风扇电压
 * @param  voltage_mv: 电压值(mV)
 * @retval None
 */
void Fan_SetVoltage(uint16_t voltage_mv)
{
    if(voltage_mv < FAN_VOLTAGE_MIN) voltage_mv = FAN_VOLTAGE_MIN;
    if(voltage_mv > FAN_VOLTAGE_MAX) voltage_mv = FAN_VOLTAGE_MAX;
    
    fan_control.voltage_mv = voltage_mv;
    
    // 计算PWM占空比
    fan_control.pwm_duty = (uint16_t)(voltage_mv * FAN_PWM_PERIOD / FAN_VOLTAGE_SUPPLY);
    
    // 设置PWM
    __HAL_TIM_SET_COMPARE(&htim_fan, TIM_CHANNEL_1, fan_control.pwm_duty);
}

/**
 * @brief  设置风扇转向
 * @param  direction: 转向
 * @retval None
 */
void Fan_SetDirection(FanDirection_t direction)
{
    fan_control.direction = direction;
    
    switch(direction)
    {
        case FAN_FORWARD:
            HAL_GPIO_WritePin(FAN_DIR1_GPIO_PORT, FAN_DIR1_GPIO_PIN, GPIO_PIN_SET);
            HAL_GPIO_WritePin(FAN_DIR2_GPIO_PORT, FAN_DIR2_GPIO_PIN, GPIO_PIN_RESET);
            break;
            
        case FAN_REVERSE:
            HAL_GPIO_WritePin(FAN_DIR1_GPIO_PORT, FAN_DIR1_GPIO_PIN, GPIO_PIN_RESET);
            HAL_GPIO_WritePin(FAN_DIR2_GPIO_PORT, FAN_DIR2_GPIO_PIN, GPIO_PIN_SET);
            break;
            
        case FAN_STOP:
        default:
            HAL_GPIO_WritePin(FAN_DIR1_GPIO_PORT, FAN_DIR1_GPIO_PIN, GPIO_PIN_RESET);
            HAL_GPIO_WritePin(FAN_DIR2_GPIO_PORT, FAN_DIR2_GPIO_PIN, GPIO_PIN_RESET);
            break;
    }
}

/**
 * @brief  获取当前电压
 * @param  None
 * @retval 电压值(mV)
 */
uint16_t Fan_GetVoltage(void)
{
    return fan_control.voltage_mv;
}

/**
 * @brief  获取当前转向
 * @param  None
 * @retval 转向
 */
FanDirection_t Fan_GetDirection(void)
{
    return fan_control.direction;
}

/**
 * @brief  检查运行状态
 * @param  None
 * @retval 1-运行, 0-停止
 */
uint8_t Fan_IsRunning(void)
{
    return fan_control.is_running;
}

/**
 * @brief  更新风扇状态
 * @param  None
 * @retval None
 */
void Fan_Update(void)
{
    if(fan_control.is_running)
    {
        fan_control.run_time_ms = HAL_GetTick() - fan_control.start_time;
    }
}
