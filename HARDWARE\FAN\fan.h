#ifndef __FAN_H
#define __FAN_H
#include "sys.h"

//////////////////////////////////////////////////////////////////////////////////	 
// 非接触式控制盘 - 风扇控制模块
// 控制12V直流风扇的启停、正反转、调速
// 使用PWM + H桥驱动电路
//////////////////////////////////////////////////////////////////////////////////

// 风扇控制引脚定义
#define FAN_PWM_GPIO_PORT   GPIOA
#define FAN_PWM_GPIO_PIN    GPIO_PIN_6      // TIM3_CH1
#define FAN_DIR1_GPIO_PORT  GPIOA
#define FAN_DIR1_GPIO_PIN   GPIO_PIN_7      // 方向控制1
#define FAN_DIR2_GPIO_PORT  GPIOG
#define FAN_DIR2_GPIO_PIN   GPIO_PIN_7      // 方向控制2
#define FAN_EN_GPIO_PORT    GPIOG
#define FAN_EN_GPIO_PIN     GPIO_PIN_8      // 使能控制

// 风扇状态枚举
typedef enum {
    FAN_STOP = 0,       // 停止
    FAN_FORWARD,        // 正转
    FAN_REVERSE         // 反转
} FanDirection_t;

// 风扇参数结构体
typedef struct {
    FanDirection_t direction;   // 转向
    uint16_t voltage_mv;        // 电压(mV) 3000-10500
    uint16_t pwm_duty;          // PWM占空比 0-1000
    uint8_t is_running;         // 运行状态
    uint32_t run_time_ms;       // 运行时间(ms)
    uint32_t start_time;        // 启动时间戳
} FanControl_t;

// 电压范围定义
#define FAN_VOLTAGE_MIN     3000    // 最小电压3.0V
#define FAN_VOLTAGE_MAX     10500   // 最大电压10.5V
#define FAN_VOLTAGE_SUPPLY  12000   // 供电电压12V

// PWM参数定义
#define FAN_PWM_PERIOD      1000    // PWM周期
#define FAN_PWM_FREQ        1000    // PWM频率1kHz

// 全局变量声明
extern FanControl_t fan_control;
extern TIM_HandleTypeDef htim_fan;

// 函数声明
void Fan_Init(void);                            // 风扇模块初始化
void Fan_Start(FanDirection_t direction);       // 启动风扇
void Fan_Stop(void);                            // 停止风扇
void Fan_SetVoltage(uint16_t voltage_mv);       // 设置电压(mV)
void Fan_SetDirection(FanDirection_t direction); // 设置转向
uint16_t Fan_GetVoltage(void);                  // 获取当前电压
FanDirection_t Fan_GetDirection(void);          // 获取当前转向
uint8_t Fan_IsRunning(void);                    // 检查运行状态
void Fan_Update(void);                          // 更新风扇状态

#endif
