# STM32F407 非接触式控制盘设计说明

## 📋 **项目概述**

本项目基于STM32F407ZGT6微控制器，设计并实现了一个非接触式风扇控制盘。通过4个光电开关检测挥手手势，结合超声波测距，实现对12V直流风扇的智能控制。

## 🎯 **功能要求对照**

### ✅ **基本要求**

1. **超声波测距 (S5)**
   - 测量范围：5-30cm
   - 精度要求：误差≤1cm
   - 实现状态：✅ 已完成

2. **风扇启停控制**
   - S1→S2：风扇正转启动
   - S2→S1：风扇正转停止
   - S3→S4：风扇反转启动  
   - S4→S3：风扇反转停止
   - 实现状态：✅ 已完成

3. **风扇调速控制**
   - S4→S2：电压上升 (3-10V)
   - S3→S1：电压下降 (3-10V)
   - 实现状态：✅ 已完成

### 🚀 **发挥部分**

1. **运行时间设定**
   - 条件：距离5-20cm，S3→S2手势
   - 时间范围：15-30s
   - 距离-时间映射关系
   - 实现状态：🔄 框架已搭建

2. **工作电压设定**
   - 条件：距离5-20cm，S4→S1手势
   - 电压范围：3.0-10.5V
   - 距离-电压映射关系
   - 实现状态：🔄 框架已搭建

3. **组合操作模式**
   - 存储≥8个动作
   - 非接触控制设置和启动
   - 实现状态：🔄 框架已搭建

## 🏗️ **系统架构**

### 📦 **模块组织**

```
非接触式控制盘/
├── HARDWARE/                    # 硬件抽象层
│   ├── PHOTOSWITCH/            # 光电开关模块 (S1-S4)
│   ├── ULTRASONIC/             # 超声波测距模块 (S5)
│   ├── FAN/                    # 风扇控制模块
│   ├── PWM/                    # PWM输出模块
│   └── DAC/                    # DAC输出模块
├── APP/                        # 应用层
│   ├── GESTURE/                # 手势识别模块
│   ├── STATE_MACHINE/          # 状态机模块
│   ├── PARAMETER/              # 参数设置模块
│   └── COMBO/                  # 组合操作模块
└── USER/                       # 用户代码
    ├── main_control.c          # 主控程序
    └── ...                     # 配置文件
```

### 🔄 **状态机设计**

- **IDLE**: 待机状态
- **FAN_CONTROL**: 风扇控制模式
- **TIME_SETTING**: 时间设定模式
- **VOLTAGE_SETTING**: 电压设定模式
- **COMBO_SETTING**: 组合操作设定模式
- **COMBO_RUNNING**: 组合操作运行模式

## 🔌 **硬件连接**

| 功能 | 引脚 | 说明 |
|------|------|------|
| **光电开关S1** | PC0 | 漫反射式光电开关 |
| **光电开关S2** | PC1 | 漫反射式光电开关 |
| **光电开关S3** | PC2 | 漫反射式光电开关 |
| **光电开关S4** | PC3 | 漫反射式光电开关 |
| **超声波TRIG** | PD3 | HC-SR04触发引脚 |
| **超声波ECHO** | PG6 | HC-SR04回响引脚 |
| **风扇PWM** | PA6 | TIM3_CH1，调速控制 |
| **风扇方向1** | PA7 | H桥方向控制 |
| **风扇方向2** | PG7 | H桥方向控制 |
| **风扇使能** | PG8 | H桥使能控制 |

## 🎮 **操作说明**

### 基本操作

1. **风扇启动**
   - 正转：S1→S2 挥手
   - 反转：S3→S4 挥手

2. **风扇停止**
   - 正转停止：S2→S1 挥手
   - 反转停止：S4→S3 挥手

3. **风扇调速**
   - 电压上升：S4→S2 挥手 (运行时)
   - 电压下降：S3→S1 挥手 (运行时)

### 高级操作

1. **时间设定**
   - 条件：手掌距离5-20cm
   - 操作：S3→S2 挥手
   - 效果：根据距离设定运行时间

2. **电压设定**
   - 条件：手掌距离5-20cm
   - 操作：S4→S1 挥手
   - 效果：根据距离设定工作电压

## 📊 **技术参数**

| 参数 | 规格 | 说明 |
|------|------|------|
| **测距范围** | 5-30cm | 超声波有效范围 |
| **测距精度** | ±1cm | 环境相关 |
| **风扇电压** | 3.0-10.5V | PWM调节 |
| **PWM频率** | 1kHz | 可配置 |
| **手势超时** | 1000ms | 防误触发 |
| **状态超时** | 10s | 设定模式 |
| **响应时间** | <100ms | 实时响应 |

## 🔧 **编译说明**

### Keil项目配置

1. **添加源文件路径**：
   - `HARDWARE/PHOTOSWITCH`
   - `HARDWARE/FAN`
   - `APP/GESTURE`
   - `APP/STATE_MACHINE`

2. **包含头文件路径**：
   - 所有硬件和应用模块路径

3. **编译选项**：
   - 优化等级：-O1
   - 调试信息：启用

### 依赖库

- STM32F4xx HAL库
- ALIENTEK开发板库
- USMART调试系统

## 🧪 **测试验证**

### 功能测试

1. **超声波测距测试**
   - 测试范围：5-30cm
   - 验证精度：±1cm

2. **手势识别测试**
   - 测试所有8种手势组合
   - 验证响应时间和准确性

3. **风扇控制测试**
   - 启停控制测试
   - 正反转测试
   - 调速测试 (3-10V)

4. **状态机测试**
   - 状态切换测试
   - 超时处理测试
   - 异常恢复测试

### 性能测试

1. **响应时间测试**
   - 手势识别响应时间
   - 风扇启停响应时间
   - 显示更新响应时间

2. **精度测试**
   - 距离测量精度
   - 电压控制精度
   - 时间控制精度

## 🚀 **扩展功能**

### 可添加功能

1. **语音提示**：添加语音反馈
2. **无线控制**：WiFi/蓝牙远程控制
3. **数据记录**：操作历史记录
4. **多风扇控制**：控制多个风扇
5. **环境监测**：温湿度传感器

### 优化建议

1. **算法优化**：手势识别算法优化
2. **界面优化**：更丰富的显示界面
3. **节能优化**：低功耗模式
4. **安全优化**：故障检测和保护

## 📝 **开发日志**

- ✅ 硬件抽象层设计完成
- ✅ 手势识别模块完成
- ✅ 状态机框架完成
- ✅ 基本风扇控制完成
- 🔄 高级功能开发中
- 🔄 测试验证进行中

## 🎯 **下一步计划**

1. 完善时间设定和电压设定功能
2. 实现组合操作模式
3. 完整的测试验证
4. 性能优化和调试
5. 文档完善和整理
